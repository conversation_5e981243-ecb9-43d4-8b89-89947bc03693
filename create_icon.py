#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة مخصصة لنظام المحاسبة
"""

import os
from pathlib import Path

def create_icon_batch():
    """إنشاء ملف batch مع أيقونة مخصصة"""
    
    # إنشاء ملف batch متقدم
    batch_content = '''@echo off
title نظام المحاسبة المتكامل
color 0A
mode con: cols=80 lines=25

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██                        نظام المحاسبة المتكامل                           ██
echo ██                     Integrated Accounting System                          ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🚀 جاري تشغيل النظام...
echo 🌐 سيفتح المتصفح تلقائياً على: http://localhost:5000
echo.
echo 📱 بيانات تسجيل الدخول:
echo    المدير العام: admin / admin123
echo    المحاسب: accountant / acc123
echo    مدير المتجر: manager / mgr123
echo.
echo ⚠️  لإيقاف النظام: أغلق هذه النافذة أو اضغط Ctrl+C
echo.
echo ████████████████████████████████████████████████████████████████████████████████

REM تغيير المجلد إلى مجلد النظام
cd /d "%~dp0نظام المحاسبة"

REM تشغيل النظام
start "" AccountingSystem.exe

REM انتظار قليل ثم فتح المتصفح
timeout /t 3 /nobreak > nul
start http://localhost:5000

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📂 إذا لم يفتح المتصفح، اذهب إلى: http://localhost:5000
echo.
pause
'''
    
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'
    if not desktop_path.exists():
        desktop_path = Path.home()
    
    batch_file = desktop_path / '🏢 نظام المحاسبة المتكامل.bat'
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ تم إنشاء ملف التشغيل المحسن: {batch_file}")
    
    return batch_file

def create_professional_launcher():
    """إنشاء مشغل احترافي"""
    
    launcher_content = '''@echo off
setlocal EnableDelayedExpansion

REM إعداد النافذة
title نظام المحاسبة المتكامل - Integrated Accounting System
color 0B
mode con: cols=90 lines=30

REM مسح الشاشة وعرض الشعار
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                                      ║
echo ║    ███╗   ██╗██████╗ ██╗   ██╗ █████╗ ███╗   ███╗    ███╗   ███╗ █████╗ ██╗         ║
echo ║    ████╗  ██║██╔══██╗██║   ██║██╔══██╗████╗ ████║    ████╗ ████║██╔══██╗██║         ║
echo ║    ██╔██╗ ██║██████╔╝██║   ██║███████║██╔████╔██║    ██╔████╔██║███████║██║         ║
echo ║    ██║╚██╗██║██╔═══╝ ██║   ██║██╔══██║██║╚██╔╝██║    ██║╚██╔╝██║██╔══██║██║         ║
echo ║    ██║ ╚████║██║     ╚██████╔╝██║  ██║██║ ╚═╝ ██║    ██║ ╚═╝ ██║██║  ██║███████╗    ║
echo ║    ╚═╝  ╚═══╝╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝     ╚═╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚══════╝    ║
echo ║                                                                                      ║
echo ║                            نظام المحاسبة المتكامل                                  ║
echo ║                         Integrated Accounting System                                ║
echo ║                                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود النظام
if not exist "%~dp0نظام المحاسبة\AccountingSystem.exe" (
    echo ❌ خطأ: لم يتم العثور على ملفات النظام
    echo 📁 تأكد من وجود مجلد "نظام المحاسبة" في نفس مكان هذا الملف
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص النظام...
timeout /t 1 /nobreak > nul
echo ✅ تم العثور على ملفات النظام

echo.
echo 🚀 جاري تشغيل النظام...
echo 📡 بدء خادم الويب...

REM تغيير المجلد وتشغيل النظام
cd /d "%~dp0نظام المحاسبة"
start "" /min AccountingSystem.exe

echo ⏳ انتظار تحميل النظام...
timeout /t 4 /nobreak > nul

echo 🌐 فتح المتصفح...
start http://localhost:5000

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                  معلومات مهمة                                      ║
echo ╠══════════════════════════════════════════════════════════════════════════════════════╣
echo ║                                                                                      ║
echo ║  🌐 رابط النظام: http://localhost:5000                                             ║
echo ║                                                                                      ║
echo ║  🔑 بيانات تسجيل الدخول:                                                           ║
echo ║     👤 المدير العام: admin / admin123                                             ║
echo ║     📊 المحاسب: accountant / acc123                                                ║
echo ║     🏪 مدير المتجر: manager / mgr123                                               ║
echo ║                                                                                      ║
echo ║  ⚠️  تعليمات مهمة:                                                                 ║
echo ║     • لا تغلق هذه النافذة أثناء استخدام النظام                                    ║
echo ║     • لإيقاف النظام: اضغط Ctrl+C أو أغلق هذه النافذة                             ║
echo ║     • احتفظ بنسخة احتياطية من قاعدة البيانات                                     ║
echo ║                                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════╝
echo.

echo ✅ تم تشغيل النظام بنجاح!
echo 🎯 النظام جاهز للاستخدام
echo.

REM انتظار إغلاق النظام
echo اضغط أي مفتاح لإيقاف النظام...
pause > nul

echo.
echo 🛑 جاري إيقاف النظام...
taskkill /f /im AccountingSystem.exe > nul 2>&1
echo ✅ تم إيقاف النظام بنجاح
echo.
echo 👋 شكراً لاستخدام نظام المحاسبة المتكامل
timeout /t 2 /nobreak > nul
'''
    
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'
    if not desktop_path.exists():
        desktop_path = Path.home()
    
    launcher_file = desktop_path / '🚀 تشغيل نظام المحاسبة (احترافي).bat'
    with open(launcher_file, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✅ تم إنشاء المشغل الاحترافي: {launcher_file}")
    
    return launcher_file

def main():
    """الدالة الرئيسية"""
    
    print("🎨 إنشاء مشغلات احترافية لنظام المحاسبة")
    print("=" * 50)
    
    # إنشاء المشغلات
    basic_launcher = create_icon_batch()
    pro_launcher = create_professional_launcher()
    
    print("\n🎉 تم إنشاء المشغلات بنجاح!")
    print("\n📋 المشغلات المتاحة:")
    print("1. 🏢 نظام المحاسبة المتكامل.bat - مشغل أساسي")
    print("2. 🚀 تشغيل نظام المحاسبة (احترافي).bat - مشغل احترافي")
    
    print("\n💡 المميزات:")
    print("• واجهة ملونة وجذابة")
    print("• فحص تلقائي لملفات النظام")
    print("• فتح المتصفح تلقائياً")
    print("• عرض بيانات تسجيل الدخول")
    print("• إيقاف آمن للنظام")
    
    print("\n🚀 للاستخدام:")
    print("انقر مرتين على أي من الملفات على سطح المكتب")

if __name__ == "__main__":
    main()
