#!/usr/bin/env python3
"""
نظام محاسبي بسيط مع تسجيل الدخول
Simple Accounting System with Login
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session

app = Flask(__name__)
app.secret_key = 'demo-secret-key-2024'

# بيانات المستخدمين
USERS = {
    'admin': {'password': 'admin123', 'name': 'أحمد المدير', 'role': 'مدير'},
    'accountant': {'password': 'acc123', 'name': 'فاطمة المحاسبة', 'role': 'محاسب'},
    'employee': {'password': 'emp123', 'name': 'محمد الموظف', 'role': 'موظف'}
}

# قالب صفحة تسجيل الدخول
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
        }
        .login-logo i {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: 1rem;
        }
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        .demo-info {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .demo-credentials {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border-right: 4px solid #ef4444;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border-right: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-logo text-center">
            <i class="fas fa-calculator"></i>
            <h2 class="fw-bold" style="color: #10b981;">نظام المحاسبة المتكامل</h2>
            <p class="text-muted">مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="demo-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>نسخة تجريبية</strong> - استخدم البيانات أدناه لتسجيل الدخول
        </div>

        <form method="POST">
            <div class="mb-3">
                <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label fw-bold">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>

        <div class="demo-credentials">
            <h6 style="color: #059669;"><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول التجريبية:</h6>
            
            <div class="mb-2">
                <strong>المدير:</strong> 
                <code>admin</code> / <code>admin123</code>
                <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('admin', 'admin123')">
                    استخدام
                </button>
            </div>
            
            <div class="mb-2">
                <strong>المحاسب:</strong> 
                <code>accountant</code> / <code>acc123</code>
                <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('accountant', 'acc123')">
                    استخدام
                </button>
            </div>
            
            <div>
                <strong>الموظف:</strong> 
                <code>employee</code> / <code>emp123</code>
                <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('employee', 'emp123')">
                    استخدام
                </button>
            </div>
        </div>

        <div class="text-center mt-4">
            <small class="text-muted">
                &copy; 2024 نظام المحاسبة المتكامل - نسخة تجريبية
            </small>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            usernameField.style.background = '#dcfce7';
            passwordField.style.background = '#dcfce7';
            
            setTimeout(() => {
                usernameField.style.background = '';
                passwordField.style.background = '';
            }, 1000);
        }
        document.getElementById('username').focus();
    </script>
</body>
</html>
'''

# قالب لوحة التحكم
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المحاسبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
        }
        .navbar {
            background: rgba(5, 150, 105, 0.95) !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border-right: 4px solid #10b981;
            border: none;
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-circle me-1"></i>{{ user_name }} ({{ user_role }})
                </span>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 bg-gradient text-white" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="fw-bold mb-2">مرحباً {{ user_name }}</h2>
                                <p class="mb-0 opacity-75">إليك نظرة سريعة على أداء شركتك اليوم</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-chart-line fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stats-number">45</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+12% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number">125,000</div>
                    <div class="text-muted">إجمالي الإيرادات (ر.س)</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+8% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stats-number">85,000</div>
                    <div class="text-muted">إجمالي المصروفات (ر.س)</div>
                    <small class="text-danger">
                        <i class="fas fa-arrow-up me-1"></i>+5% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stats-number">40,000</div>
                    <div class="text-muted">صافي الربح (ر.س)</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+15% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-8 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">الإيرادات والمصروفات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">توزيع المصروفات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="expenseChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2 text-primary"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 py-3" onclick="showDemo('invoice')">
                                    <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                                    فاتورة جديدة
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 py-3" onclick="showDemo('expense')">
                                    <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                                    مصروف جديد
                                </button>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ url_for('reports') }}" class="btn btn-outline-info w-100 py-3">
                                    <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                    التقارير المالية
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 py-3" onclick="showDemo('settings')">
                                    <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                    الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: [18000, 22000, 19000, 28000, 25000, 32000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'المصروفات',
                        data: [12000, 15000, 13000, 18000, 16000, 20000],
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'top' } },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ر.س';
                                }
                            }
                        }
                    }
                }
            });

            const expenseCtx = document.getElementById('expenseChart').getContext('2d');
            new Chart(expenseCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الإيجار', 'الرواتب', 'المرافق', 'التسويق', 'أخرى'],
                    datasets: [{
                        data: [35, 30, 15, 12, 8],
                        backgroundColor: ['#10b981', '#22c55e', '#84cc16', '#059669', '#047857']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        });

        function showDemo(type) {
            const messages = {
                'invoice': 'في النسخة الكاملة، ستتمكن من إنشاء فواتير جديدة بسهولة!',
                'expense': 'في النسخة الكاملة، ستتمكن من تسجيل المصروفات وتصنيفها!',
                'report': 'في النسخة الكاملة، ستتمكن من إنشاء تقارير مالية شاملة!',
                'settings': 'في النسخة الكاملة، ستتمكن من إدارة إعدادات الشركة!'
            };
            alert(messages[type] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
        }
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        
        print(f"محاولة تسجيل دخول: {username} / {password}")  # للتشخيص
        
        if username in USERS and USERS[username]['password'] == password:
            session['user_id'] = username
            session['user_name'] = USERS[username]['name']
            session['user_role'] = USERS[username]['role']
            flash(f'مرحباً {USERS[username]["name"]}!', 'success')
            print(f"تم تسجيل الدخول بنجاح: {username}")  # للتشخيص
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            print(f"فشل تسجيل الدخول: {username}")  # للتشخيص
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                user_name=session.get('user_name', 'مستخدم'),
                                user_role=session.get('user_role', 'مستخدم'))

@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # صفحة التقارير المالية
    reports_template = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التقارير المالية - نظام المحاسبة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            * { font-family: 'Cairo', sans-serif; }
            body {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                min-height: 100vh;
            }
            .navbar { background: rgba(5, 150, 105, 0.95) !important; }
            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .card:hover { transform: translateY(-5px); }
            .btn-primary { background: linear-gradient(135deg, #10b981, #059669); border: none; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-calculator me-2"></i>
                    نظام المحاسبة المتكامل
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user-circle me-1"></i>{{ user_name }}
                    </span>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row mb-4">
                <div class="col-md-8">
                    <h2 class="text-white fw-bold">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير المالية
                    </h2>
                    <p class="text-white-50">تقارير شاملة ومفصلة لأداء شركتك المالي</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3">
                                <i class="fas fa-file-invoice-dollar fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">تقرير الدخل</h5>
                            <p class="text-muted">قائمة الدخل والإيرادات والمصروفات</p>
                            <button class="btn btn-primary" onclick="showDemo('income')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="fas fa-balance-scale fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">الميزانية العمومية</h5>
                            <p class="text-muted">الأصول والخصوم وحقوق الملكية</p>
                            <button class="btn btn-success" onclick="showDemo('balance')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-info mb-3">
                                <i class="fas fa-chart-line fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">التدفق النقدي</h5>
                            <p class="text-muted">حركة النقد الداخل والخارج</p>
                            <button class="btn btn-info" onclick="showDemo('cashflow')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3">
                                <i class="fas fa-chart-pie fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">تحليل المصروفات</h5>
                            <p class="text-muted">تفصيل المصروفات حسب الفئات</p>
                            <button class="btn btn-warning" onclick="showDemo('expenses')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-danger mb-3">
                                <i class="fas fa-users fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">تقرير الرواتب</h5>
                            <p class="text-muted">ملخص رواتب الموظفين والاستقطاعات</p>
                            <button class="btn btn-danger" onclick="showDemo('payroll')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card text-center">
                        <div class="card-body p-4">
                            <div class="text-secondary mb-3">
                                <i class="fas fa-calendar-alt fa-3x"></i>
                            </div>
                            <h5 class="fw-bold">التقارير الدورية</h5>
                            <p class="text-muted">تقارير شهرية وربع سنوية وسنوية</p>
                            <button class="btn btn-secondary" onclick="showDemo('periodic')">
                                <i class="fas fa-eye me-2"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2 text-primary"></i>
                                معلومات التقارير
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>في النسخة الكاملة من النظام:</h6>
                                <ul class="mb-0">
                                    <li>تقارير مالية تفاعلية ومفصلة</li>
                                    <li>إمكانية تصدير التقارير إلى PDF و Excel</li>
                                    <li>تقارير مخصصة حسب الفترة الزمنية</li>
                                    <li>رسوم بيانية متقدمة وتحليلات</li>
                                    <li>مقارنات بين الفترات المختلفة</li>
                                    <li>تقارير ضريبية متوافقة مع اللوائح السعودية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function showDemo(type) {
                const messages = {
                    'income': 'تقرير الدخل: يعرض الإيرادات والمصروفات وصافي الربح للفترة المحددة',
                    'balance': 'الميزانية العمومية: تعرض الأصول والخصوم وحقوق الملكية في تاريخ محدد',
                    'cashflow': 'التدفق النقدي: يتتبع حركة النقد من الأنشطة التشغيلية والاستثمارية والتمويلية',
                    'expenses': 'تحليل المصروفات: يفصل المصروفات حسب الفئات مع نسب ومقارنات',
                    'payroll': 'تقرير الرواتب: يعرض ملخص رواتب الموظفين والاستقطاعات والمكافآت',
                    'periodic': 'التقارير الدورية: تقارير شهرية وربع سنوية وسنوية للمتابعة المستمرة'
                };

                alert('📊 ' + (messages[type] || 'تقرير مالي متقدم') + '\\n\\n🚀 هذه الميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    </body>
    </html>
    '''

    return render_template_string(reports_template,
                                user_name=session.get('user_name', 'مستخدم'))

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🚀 تم تشغيل النظام المحاسبي!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123") 
    print("   الموظف: employee / emp123")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
