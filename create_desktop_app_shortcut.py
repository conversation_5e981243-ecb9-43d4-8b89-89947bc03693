#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء اختصار لتطبيق سطح المكتب
"""

import os
import shutil
from pathlib import Path

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    
    print("🖥️  إنشاء اختصار تطبيق سطح المكتب")
    print("=" * 40)
    
    # مسار سطح المكتب
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'
    if not desktop_path.exists():
        desktop_path = Path.home()
    
    # إنشاء مجلد التطبيق على سطح المكتب
    app_folder = desktop_path / 'نظام المحاسبة - سطح المكتب'
    if app_folder.exists():
        shutil.rmtree(app_folder)
    app_folder.mkdir()
    
    # نسخ الملفات
    files_to_copy = [
        'desktop_accounting.py',
        'run_desktop_app.bat'
    ]
    
    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, app_folder)
            print(f"📄 تم نسخ {file_name}")
    
    # إنشاء ملف تشغيل مباشر
    direct_run_content = f'''@echo off
cd /d "{app_folder}"
py desktop_accounting.py
'''
    
    direct_run_file = desktop_path / '🖥️ نظام المحاسبة (سطح المكتب).bat'
    with open(direct_run_file, 'w', encoding='utf-8') as f:
        f.write(direct_run_content)
    
    print(f"✅ تم إنشاء اختصار مباشر: {direct_run_file}")
    
    # إنشاء ملف تعليمات
    instructions_content = '''# نظام المحاسبة - تطبيق سطح المكتب

## 🖥️ تطبيق Windows حقيقي

هذا تطبيق سطح مكتب حقيقي بواجهة Windows تقليدية - ليس نظام ويب!

## 🚀 كيفية التشغيل

### الطريقة الأولى (الأسهل):
انقر مرتين على: "🖥️ نظام المحاسبة (سطح المكتب).bat"

### الطريقة الثانية:
1. اذهب إلى مجلد "نظام المحاسبة - سطح المكتب"
2. انقر مرتين على "run_desktop_app.bat"

## 🔑 بيانات تسجيل الدخول

- المدير العام: admin / admin123
- المحاسب: accountant / acc123  
- مدير المتجر: manager / mgr123

## 💻 المميزات

✅ واجهة Windows تقليدية
✅ نوافذ وقوائم حقيقية
✅ لا يحتاج متصفح
✅ يعمل بدون إنترنت
✅ قاعدة بيانات محلية
✅ أمان عالي

## 📋 الوظائف المتاحة

- ✅ تسجيل الدخول الآمن
- ✅ لوحة تحكم بالإحصائيات
- ✅ إدارة المنتجات (إضافة، تعديل، عرض)
- ✅ إدارة العملاء (إضافة، تعديل، عرض)
- 🔄 إدارة الفواتير (قيد التطوير)
- 🔄 التقارير (قيد التطوير)

## ⚠️ متطلبات النظام

- Windows 7 أو أحدث
- Python 3.6 أو أحدث
- مساحة 50 MB على القرص الصلب

## 🔧 حل المشاكل

### المشكلة: Python غير موجود
الحل: تثبيت Python من python.org

### المشكلة: خطأ في التشغيل
الحل: تشغيل كمدير (Run as Administrator)

---
هذا تطبيق سطح مكتب حقيقي - ليس نظام ويب!
'''
    
    instructions_file = app_folder / 'تعليمات التشغيل.txt'
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions_content)
    
    print(f"✅ تم إنشاء ملف التعليمات")
    
    return app_folder, direct_run_file

def main():
    """الدالة الرئيسية"""
    
    app_folder, shortcut_file = create_desktop_shortcut()
    
    print("\n🎉 تم إنشاء تطبيق سطح المكتب بنجاح!")
    print(f"\n📁 مجلد التطبيق: {app_folder}")
    print(f"🖥️  اختصار التشغيل: {shortcut_file}")
    
    print("\n📋 الملفات المُنشأة:")
    print("1. مجلد 'نظام المحاسبة - سطح المكتب' على سطح المكتب")
    print("2. اختصار '🖥️ نظام المحاسبة (سطح المكتب).bat'")
    
    print("\n🚀 للتشغيل:")
    print("انقر مرتين على الاختصار على سطح المكتب")
    
    print("\n✅ هذا تطبيق سطح مكتب حقيقي!")
    print("💻 واجهة Windows تقليدية")
    print("🚫 لا يحتاج متصفح أو إنترنت")

if __name__ == "__main__":
    main()
