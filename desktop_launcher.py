#!/usr/bin/env python3
"""
مشغل سطح المكتب لنظام المحاسبة
Desktop Launcher for Accounting System
"""

import os
import sys
import time
import threading
import webbrowser
import tkinter as tk
from tkinter import messagebox
from accounting_system import app

class DesktopLauncher:
    """مشغل سطح المكتب"""
    
    def __init__(self):
        self.server_running = False
        self.create_system_tray()
    
    def create_system_tray(self):
        """إنشاء نافذة تحكم بسيطة"""
        
        self.root = tk.Tk()
        self.root.title("نظام المحاسبة - مشغل سطح المكتب")
        self.root.geometry("500x400")
        self.root.configure(bg='#f0f9ff')
        
        # منع إغلاق النافذة بالخطأ
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # العنوان
        title_frame = tk.Frame(self.root, bg='#10b981', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        tk.Label(
            title_frame,
            text="🧮 نظام المحاسبة المتكامل",
            font=('Arial', 16, 'bold'),
            bg='#10b981',
            fg='white'
        ).pack(expand=True)
        
        # منطقة المعلومات
        info_frame = tk.Frame(self.root, bg='#f0f9ff')
        info_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # حالة الخادم
        self.status_label = tk.Label(
            info_frame,
            text="🔄 جاري تشغيل النظام...",
            font=('Arial', 12),
            bg='#f0f9ff',
            fg='#374151'
        )
        self.status_label.pack(pady=10)
        
        # معلومات الوصول
        tk.Label(
            info_frame,
            text="📱 عنوان النظام:",
            font=('Arial', 11, 'bold'),
            bg='#f0f9ff'
        ).pack(pady=(20,5))
        
        url_frame = tk.Frame(info_frame, bg='white', relief='solid', bd=1)
        url_frame.pack(fill='x', pady=5)
        
        tk.Label(
            url_frame,
            text="http://localhost:5000",
            font=('Arial', 12, 'bold'),
            bg='white',
            fg='#10b981'
        ).pack(pady=10)
        
        # بيانات تسجيل الدخول
        tk.Label(
            info_frame,
            text="🔑 بيانات تسجيل الدخول:",
            font=('Arial', 11, 'bold'),
            bg='#f0f9ff'
        ).pack(pady=(20,5))
        
        login_frame = tk.Frame(info_frame, bg='white', relief='solid', bd=1)
        login_frame.pack(fill='x', pady=5)
        
        login_info = [
            "المدير: admin / admin123",
            "المحاسب: accountant / acc123",
            "الموظف: employee / emp123"
        ]
        
        for info in login_info:
            tk.Label(
                login_frame,
                text=f"• {info}",
                font=('Arial', 10),
                bg='white',
                anchor='w'
            ).pack(fill='x', padx=10, pady=2)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(info_frame, bg='#f0f9ff')
        buttons_frame.pack(fill='x', pady=20)
        
        self.open_btn = tk.Button(
            buttons_frame,
            text="🌐 فتح النظام في المتصفح",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.open_browser,
            padx=20,
            pady=10,
            state='disabled'
        )
        self.open_btn.pack(fill='x', pady=5)
        
        tk.Button(
            buttons_frame,
            text="🔄 إعادة تشغيل النظام",
            font=('Arial', 11),
            bg='#059669',
            fg='white',
            command=self.restart_system,
            padx=20,
            pady=8
        ).pack(fill='x', pady=5)
        
        tk.Button(
            buttons_frame,
            text="❌ إغلاق النظام",
            font=('Arial', 11),
            bg='#dc2626',
            fg='white',
            command=self.close_system,
            padx=20,
            pady=8
        ).pack(fill='x', pady=5)
        
        # تشغيل الخادم
        self.start_server()
    
    def start_server(self):
        """تشغيل خادم Flask"""
        
        def run_flask():
            try:
                # تعطيل وضع التطوير لإخفاء التحذيرات
                os.environ['FLASK_ENV'] = 'production'
                app.config['DEBUG'] = False
                
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
                self.server_running = True
            except Exception as e:
                self.root.after(0, lambda: self.show_error(f"فشل في تشغيل الخادم: {e}"))
        
        # تشغيل الخادم في خيط منفصل
        server_thread = threading.Thread(target=run_flask)
        server_thread.daemon = True
        server_thread.start()
        
        # التحقق من حالة الخادم
        self.root.after(3000, self.check_server)
    
    def check_server(self):
        """التحقق من حالة الخادم"""
        
        try:
            import urllib.request
            urllib.request.urlopen('http://localhost:5000', timeout=2)
            
            # الخادم يعمل
            self.status_label.config(
                text="✅ النظام يعمل بنجاح!",
                fg='#059669'
            )
            self.open_btn.config(state='normal')
            
            # فتح المتصفح تلقائياً
            self.root.after(1000, self.open_browser)
            
        except:
            # الخادم لا يعمل بعد
            self.status_label.config(
                text="⏳ جاري تشغيل النظام...",
                fg='#f59e0b'
            )
            self.root.after(2000, self.check_server)
    
    def open_browser(self):
        """فتح المتصفح"""
        try:
            webbrowser.open('http://localhost:5000')
            self.status_label.config(
                text="✅ تم فتح النظام في المتصفح!",
                fg='#059669'
            )
        except Exception as e:
            self.show_error(f"فشل في فتح المتصفح: {e}")
    
    def restart_system(self):
        """إعادة تشغيل النظام"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تشغيل النظام؟"):
            self.status_label.config(
                text="🔄 جاري إعادة التشغيل...",
                fg='#f59e0b'
            )
            self.open_btn.config(state='disabled')
            
            # إعادة تشغيل
            self.root.after(1000, self.start_server)
    
    def close_system(self):
        """إغلاق النظام"""
        if messagebox.askyesno("تأكيد", "هل تريد إغلاق النظام؟"):
            self.root.quit()
    
    def on_closing(self):
        """عند محاولة إغلاق النافذة"""
        if messagebox.askyesno("تأكيد", "هل تريد إغلاق النظام؟\n\nسيتم إيقاف خادم المحاسبة."):
            self.root.quit()
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        messagebox.showerror("خطأ", message)
        self.status_label.config(
            text="❌ حدث خطأ في النظام",
            fg='#dc2626'
        )
    
    def run(self):
        """تشغيل المشغل"""
        
        print("🖥️ تشغيل مشغل سطح المكتب...")
        print("📱 سيتم فتح نافذة التحكم...")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف النظام")
        except Exception as e:
            print(f"❌ خطأ: {e}")

def main():
    """الوظيفة الرئيسية"""
    
    # التحقق من المتطلبات
    try:
        import tkinter
    except ImportError:
        print("❌ tkinter غير متوفر")
        print("💡 قم بتثبيت Python مع tkinter")
        return
    
    # تشغيل المشغل
    launcher = DesktopLauncher()
    launcher.run()

if __name__ == '__main__':
    main()
