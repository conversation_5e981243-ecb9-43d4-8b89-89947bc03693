# 🌐 دليل الوصول عن بُعد - نظام المحاسبة

## 📋 نظرة عامة

يوفر نظام المحاسبة إمكانية الوصول عن بُعد الآمن للدعم التقني والصيانة، مما يتيح للمطورين تقديم الدعم والتحديثات دون الحاجة للحضور الفعلي.

## 🔐 الأمان والحماية

### مستويات الأمان:
- **رموز وصول فريدة** لكل عميل
- **صلاحيات محددة** لكل رمز
- **تواريخ انتهاء صلاحية** قابلة للتخصيص
- **تسجيل شامل** لجميع محاولات الوصول
- **تشفير الاتصالات** عبر HTTPS

## 🛠️ كيفية الاستخدام

### للمدير (إنشاء رموز الوصول):

1. **الدخول للنظام** بحساب المدير
2. **الذهاب لقائمة "أنظمة متقدمة"**
3. **اختيار "الوصول عن بُعد"**
4. **إنشاء رمز جديد** بالمعلومات التالية:
   - اسم العميل
   - معرف العميل الفريد
   - مدة الصلاحية
   - العمليات المسموحة

### للمطور (الوصول عن بُعد):

#### 1. الحصول على معلومات النظام:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://CLIENT_IP:5000/api/remote/view
```

#### 2. إنشاء نسخة احتياطية:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X POST http://CLIENT_IP:5000/api/remote/backup
```

#### 3. فحص حالة النظام:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://CLIENT_IP:5000/api/remote/system
```

## 🔧 أنواع العمليات المتاحة

### 1. **عرض البيانات (view)**
- إحصائيات النظام
- عدد المنتجات والعملاء
- حالة النظام العامة

### 2. **النسخ الاحتياطي (backup)**
- إنشاء نسخة احتياطية فورية
- ضغط قاعدة البيانات
- تحميل النسخة الاحتياطية

### 3. **معلومات النظام (system)**
- معلومات الخادم
- استخدام المعالج والذاكرة
- مساحة القرص الصلب

### 4. **تعديل البيانات (edit)**
- تعديل الإعدادات
- إصلاح البيانات
- تحديث النظام

## 📊 مراقبة الوصول

### سجل الاتصالات:
- **الوقت والتاريخ** لكل محاولة وصول
- **عنوان IP** للمستخدم
- **نوع العملية** المطلوبة
- **حالة النجاح/الفشل**
- **تفاصيل إضافية** عن العملية

### الإحصائيات:
- إجمالي رموز الوصول
- الرموز النشطة
- عدد الاتصالات الإجمالي

## 🚨 إجراءات الطوارئ

### في حالة مشكلة أمنية:
1. **إلغاء تفعيل جميع الرموز** فوراً
2. **مراجعة سجل الاتصالات** للتحقق من النشاط المشبوه
3. **إنشاء رموز جديدة** بصلاحيات محدودة

### في حالة فقدان الوصول:
1. **التواصل مع العميل** لإنشاء رمز جديد
2. **استخدام الوصول المحلي** إذا أمكن
3. **إعادة تعيين النظام** كحل أخير

## 📞 السيناريوهات الشائعة

### 1. **صيانة دورية:**
```bash
# فحص حالة النظام
curl -H "Authorization: Bearer TOKEN" http://IP:5000/api/remote/system

# إنشاء نسخة احتياطية
curl -H "Authorization: Bearer TOKEN" -X POST http://IP:5000/api/remote/backup
```

### 2. **حل مشكلة عاجلة:**
```bash
# عرض إحصائيات سريعة
curl -H "Authorization: Bearer TOKEN" http://IP:5000/api/remote/view

# الوصول للنظام لحل المشكلة
# (يتطلب صلاحية edit)
```

### 3. **تحديث النظام:**
```bash
# نسخة احتياطية قبل التحديث
curl -H "Authorization: Bearer TOKEN" -X POST http://IP:5000/api/remote/backup

# تطبيق التحديث
# (عبر الوصول المباشر أو API خاص)
```

## ⚠️ تحذيرات مهمة

1. **لا تشارك رموز الوصول** مع أطراف ثالثة
2. **احذف الرموز المنتهية الصلاحية** بانتظام
3. **راقب سجل الاتصالات** للنشاط المشبوه
4. **استخدم اتصالات HTTPS** دائماً في البيئة الإنتاجية
5. **قم بنسخ احتياطية منتظمة** قبل أي تعديل

## 🔄 أفضل الممارسات

### للمدراء:
- إنشاء رموز بصلاحيات محدودة
- تحديد مدة صلاحية قصيرة للمهام العاجلة
- مراجعة سجل الوصول بانتظام

### للمطورين:
- استخدام الحد الأدنى من الصلاحيات المطلوبة
- توثيق جميع العمليات المنجزة
- إبلاغ العميل بانتهاء المهمة

## 📈 التطوير المستقبلي

### ميزات مخططة:
- **واجهة ويب للوصول عن بُعد** بدلاً من API فقط
- **إشعارات فورية** عند محاولات الوصول
- **تسجيل فيديو** للجلسات (اختياري)
- **تكامل مع أدوات المراقبة** الخارجية

---

**تم تطوير هذا النظام لضمان أعلى مستويات الأمان والكفاءة في الدعم التقني عن بُعد.**
