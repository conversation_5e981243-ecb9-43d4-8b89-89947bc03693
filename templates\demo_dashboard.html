{% extends "demo_base.html" %}

{% block title %}لوحة التحكم - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="welcome-hero">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="welcome-content">
                        <h1 class="display-5 fw-bold text-white mb-3">
                            <i class="fas fa-chart-line me-3"></i>
                            مرحباً في لوحة التحكم
                        </h1>
                        <p class="lead text-white-50 mb-4">
                            إليك نظرة شاملة على الأداء المالي لشركتك مع أحدث الإحصائيات والتقارير
                        </p>
                        <div class="d-flex gap-3">
                            <button class="btn btn-light btn-lg" onclick="showDemo('report')">
                                <i class="fas fa-download me-2"></i>تحميل التقرير
                            </button>
                            <button class="btn btn-outline-light btn-lg" onclick="showDemo('export')">
                                <i class="fas fa-share me-2"></i>مشاركة البيانات
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="welcome-animation">
                        <div class="floating-card">
                            <div class="mini-chart">
                                <div class="chart-bars">
                                    <div class="bar" style="height: 60%; animation-delay: 0.1s;"></div>
                                    <div class="bar" style="height: 80%; animation-delay: 0.2s;"></div>
                                    <div class="bar" style="height: 45%; animation-delay: 0.3s;"></div>
                                    <div class="bar" style="height: 90%; animation-delay: 0.4s;"></div>
                                    <div class="bar" style="height: 70%; animation-delay: 0.5s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-5">
    <div class="col-12 mb-4">
        <h3 class="text-white fw-bold">
            <i class="fas fa-chart-bar me-2"></i>
            الإحصائيات المالية
        </h3>
        <p class="text-white-50">نظرة سريعة على الأداء المالي الحالي</p>
    </div>

    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="modern-stats-card primary">
            <div class="stats-header">
                <div class="stats-icon-modern">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <div class="stats-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12%</span>
                </div>
            </div>
            <div class="stats-body">
                <h2 class="stats-number-modern">{{ stats.total_invoices }}</h2>
                <p class="stats-label-modern">إجمالي الفواتير</p>
                <div class="stats-progress">
                    <div class="progress-bar" style="width: 75%;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="modern-stats-card success">
            <div class="stats-header">
                <div class="stats-icon-modern">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8%</span>
                </div>
            </div>
            <div class="stats-body">
                <h2 class="stats-number-modern">{{ "{:,.0f}".format(stats.total_revenue) }}</h2>
                <p class="stats-label-modern">إجمالي الإيرادات (ر.س)</p>
                <div class="stats-progress">
                    <div class="progress-bar" style="width: 85%;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="modern-stats-card warning">
            <div class="stats-header">
                <div class="stats-icon-modern">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="stats-trend negative">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5%</span>
                </div>
            </div>
            <div class="stats-body">
                <h2 class="stats-number-modern">{{ "{:,.0f}".format(stats.total_expenses) }}</h2>
                <p class="stats-label-modern">إجمالي المصروفات (ر.س)</p>
                <div class="stats-progress">
                    <div class="progress-bar" style="width: 60%;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="modern-stats-card profit">
            <div class="stats-header">
                <div class="stats-icon-modern">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15%</span>
                </div>
            </div>
            <div class="stats-body">
                <h2 class="stats-number-modern">{{ "{:,.0f}".format(stats.net_profit) }}</h2>
                <p class="stats-label-modern">صافي الربح (ر.س)</p>
                <div class="stats-progress">
                    <div class="progress-bar" style="width: 90%;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-5">
    <div class="col-12 mb-4">
        <div class="section-title">
            <i class="fas fa-chart-area"></i>
            <div>
                <h3 class="text-white fw-bold mb-1">التحليلات المالية</h3>
                <p class="text-white-50 mb-0">رسوم بيانية تفاعلية لتتبع الأداء المالي</p>
            </div>
        </div>
    </div>

    <div class="col-xl-8 mb-4">
        <div class="enhanced-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="fw-bold mb-1">
                            <i class="fas fa-chart-line text-success me-2"></i>
                            الإيرادات والمصروفات الشهرية
                        </h5>
                        <small class="text-muted">مقارنة الأداء المالي خلال الأشهر الماضية</small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-success active">6 أشهر</button>
                        <button class="btn btn-outline-success">سنة</button>
                        <button class="btn btn-outline-success">مخصص</button>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-4 mb-4">
        <div class="enhanced-card">
            <div class="card-header">
                <div>
                    <h5 class="fw-bold mb-1">
                        <i class="fas fa-chart-pie text-success me-2"></i>
                        توزيع المصروفات
                    </h5>
                    <small class="text-muted">تحليل المصروفات حسب الفئات</small>
                </div>
            </div>
            <div class="card-body p-4">
                <canvas id="expenseChart" height="200"></canvas>
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">الإيجار</span>
                        <span class="badge bg-success">35%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">الرواتب</span>
                        <span class="badge bg-info">30%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small">أخرى</span>
                        <span class="badge bg-secondary">35%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activities and Tasks -->
<div class="row">
    <div class="col-xl-6 mb-4">
        <div class="enhanced-card">
            <div class="card-header">
                <div>
                    <h5 class="fw-bold mb-1">
                        <i class="fas fa-history text-success me-2"></i>
                        الأنشطة الحديثة
                    </h5>
                    <small class="text-muted">آخر العمليات والتحديثات</small>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="activity-timeline">
                    {% for activity in recent_activities %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="{{ activity.icon }}"></i>
                        </div>
                        <div class="activity-content">
                            <h6 class="activity-title">{{ activity.title }}</h6>
                            <p class="activity-description">{{ activity.description }}</p>
                            <span class="activity-time">{{ activity.time_ago }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center p-3 border-top">
                    <button class="btn btn-outline-success btn-sm" onclick="showDemo('activities')">
                        <i class="fas fa-eye me-1"></i>عرض جميع الأنشطة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 mb-3">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2 text-primary"></i>
                    المهام المعلقة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-warning mb-2">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-warning">{{ pending_tasks.overdue_invoices }}</h4>
                            <small class="text-muted">فواتير متأخرة</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-info mb-2">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-info">{{ pending_tasks.pending_approvals }}</h4>
                            <small class="text-muted">في انتظار الموافقة</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-success mb-2">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-success">{{ pending_tasks.completed_today }}</h4>
                            <small class="text-muted">مكتملة اليوم</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-primary mb-2">
                                <i class="fas fa-calendar-alt fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-primary">{{ pending_tasks.due_this_week }}</h4>
                            <small class="text-muted">مستحقة هذا الأسبوع</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary w-100 py-3" onclick="showDemo('invoice')">
                            <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                            فاتورة جديدة
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-success w-100 py-3" onclick="showDemo('expense')">
                            <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                            مصروف جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('reports') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                            تقرير الدخل
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning w-100 py-3" onclick="showDemo('settings')">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Welcome Hero Section */
.welcome-hero {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
    border-radius: 25px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.welcome-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.welcome-content {
    position: relative;
    z-index: 2;
}

.floating-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.mini-chart {
    height: 120px;
    display: flex;
    align-items: end;
    justify-content: center;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 100%;
}

.bar {
    width: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
    border-radius: 4px;
    animation: growUp 2s ease-out;
}

@keyframes growUp {
    from { height: 0; }
    to { height: var(--height); }
}

/* Modern Stats Cards */
.modern-stats-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.modern-stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modern-stats-card.primary { border-color: #10b981; }
.modern-stats-card.success { border-color: #22c55e; }
.modern-stats-card.warning { border-color: #84cc16; }
.modern-stats-card.profit { border-color: #059669; }

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.stats-icon-modern {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #10b981, #059669);
}

.modern-stats-card.success .stats-icon-modern {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.modern-stats-card.warning .stats-icon-modern {
    background: linear-gradient(135deg, #84cc16, #65a30d);
}

.modern-stats-card.profit .stats-icon-modern {
    background: linear-gradient(135deg, #059669, #047857);
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
}

.stats-trend.positive {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.stats-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.stats-number-modern {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-label-modern {
    color: #64748b;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stats-progress {
    height: 6px;
    background: #f1f5f9;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #22c55e);
    border-radius: 3px;
    transition: width 2s ease;
}

/* Enhanced Cards */
.enhanced-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.enhanced-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(16, 185, 129, 0.3);
}

.enhanced-card .card-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-bottom: 2px solid #10b981;
    border-radius: 20px 20px 0 0 !important;
    padding: 1.5rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.section-title i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'الإيرادات',
                data: [18000, 22000, 19000, 28000, 25000, 32000],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'المصروفات',
                data: [12000, 15000, 13000, 18000, 16000, 20000],
                borderColor: '#059669',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });

    // Expense Chart
    const expenseCtx = document.getElementById('expenseChart').getContext('2d');
    new Chart(expenseCtx, {
        type: 'doughnut',
        data: {
            labels: ['الإيجار', 'الرواتب', 'المرافق', 'التسويق', 'أخرى'],
            datasets: [{
                data: [35, 30, 15, 12, 8],
                backgroundColor: [
                    '#10b981',
                    '#22c55e',
                    '#84cc16',
                    '#059669',
                    '#047857'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});

function showDemo(type) {
    const messages = {
        'invoice': 'في النسخة الكاملة، ستتمكن من إنشاء فواتير جديدة بسهولة!',
        'expense': 'في النسخة الكاملة، ستتمكن من تسجيل المصروفات وتصنيفها!',
        'settings': 'في النسخة الكاملة، ستتمكن من إدارة إعدادات الشركة والنظام!'
    };
    
    alert(messages[type] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
}
</script>
{% endblock %}
