#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حل نهائي بسيط وعملي لنظام المحاسبة
"""

import os
import shutil
from pathlib import Path

def create_simple_desktop_solution():
    """إنشاء حل بسيط لسطح المكتب"""
    
    print("🖥️  إنشاء حل سطح المكتب البسيط")
    print("=" * 40)
    
    # مسار سطح المكتب
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'
    if not desktop_path.exists():
        desktop_path = Path.home()
    
    # إنشاء مجلد النظام على سطح المكتب
    system_folder = desktop_path / 'نظام المحاسبة'
    if system_folder.exists():
        shutil.rmtree(system_folder)
    system_folder.mkdir()
    
    # نسخ الملفات الأساسية
    files_to_copy = [
        'accounting_system_db.py',
        'accounting_system.db',
        'requirements.txt'
    ]
    
    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, system_folder)
            print(f"📄 تم نسخ {file_name}")
    
    # إنشاء ملف تثبيت المكتبات
    install_content = '''@echo off
title تثبيت مكتبات نظام المحاسبة
echo جاري تثبيت المكتبات المطلوبة...
py -m pip install flask
echo تم تثبيت المكتبات بنجاح!
pause
'''
    
    install_file = system_folder / 'تثبيت المكتبات.bat'
    with open(install_file, 'w', encoding='utf-8') as f:
        f.write(install_content)
    
    print(f"✅ تم إنشاء ملف تثبيت المكتبات")
    
    # إنشاء ملف تشغيل النظام
    run_content = '''@echo off
title نظام المحاسبة المتكامل
color 0A
cls
echo.
echo ================================================
echo           نظام المحاسبة المتكامل
echo ================================================
echo.
echo 🚀 جاري تشغيل النظام...
echo 🌐 سيفتح المتصفح على: http://localhost:5000
echo.
echo 🔑 بيانات تسجيل الدخول:
echo    المدير: admin / admin123
echo    المحاسب: accountant / acc123
echo    مدير المتجر: manager / mgr123
echo.
echo ⚠️  لإيقاف النظام: اضغط Ctrl+C
echo ================================================
echo.

REM تشغيل النظام
py accounting_system_db.py

echo.
echo تم إغلاق النظام
pause
'''
    
    run_file = system_folder / 'تشغيل النظام.bat'
    with open(run_file, 'w', encoding='utf-8') as f:
        f.write(run_content)
    
    print(f"✅ تم إنشاء ملف تشغيل النظام")
    
    # إنشاء ملف تعليمات
    instructions_content = '''# نظام المحاسبة المتكامل

## 🚀 خطوات التشغيل

### المرة الأولى فقط:
1. انقر مرتين على "تثبيت المكتبات.bat"
2. انتظر حتى ينتهي التثبيت

### كل مرة تريد تشغيل النظام:
1. انقر مرتين على "تشغيل النظام.bat"
2. انتظر حتى يظهر "Running on http://127.0.0.1:5000"
3. افتح المتصفح واذهب إلى: http://localhost:5000

## 🔑 بيانات تسجيل الدخول

- المدير العام: admin / admin123
- المحاسب: accountant / acc123  
- مدير المتجر: manager / mgr123

## 📁 الملفات

- accounting_system_db.py - ملف النظام الرئيسي
- accounting_system.db - قاعدة البيانات
- تثبيت المكتبات.bat - تثبيت المكتبات (مرة واحدة فقط)
- تشغيل النظام.bat - تشغيل النظام
- تعليمات.txt - هذا الملف

## ⚠️ ملاحظات مهمة

1. تأكد من وجود Python مثبت على الجهاز
2. قم بتشغيل "تثبيت المكتبات.bat" في المرة الأولى فقط
3. لا تحذف أي ملف من الملفات
4. احتفظ بنسخة احتياطية من قاعدة البيانات

## 🔧 حل المشاكل

### المشكلة: Python غير موجود
الحل: تثبيت Python من python.org

### المشكلة: خطأ في المكتبات
الحل: تشغيل "تثبيت المكتبات.bat" مرة أخرى

### المشكلة: النظام لا يفتح
الحل: تأكد من أن المنفذ 5000 غير مستخدم

---
للدعم التقني، احتفظ بهذا الملف
'''
    
    instructions_file = system_folder / 'تعليمات.txt'
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions_content)
    
    print(f"✅ تم إنشاء ملف التعليمات")
    
    # إنشاء اختصار على سطح المكتب
    shortcut_content = f'''@echo off
cd /d "{system_folder}"
"تشغيل النظام.bat"
'''
    
    shortcut_file = desktop_path / '🏢 نظام المحاسبة.bat'
    with open(shortcut_file, 'w', encoding='utf-8') as f:
        f.write(shortcut_content)
    
    print(f"✅ تم إنشاء اختصار سطح المكتب")
    
    return system_folder

def main():
    """الدالة الرئيسية"""
    
    # إنشاء الحل البسيط
    system_folder = create_simple_desktop_solution()
    
    print("\n🎉 تم إنشاء الحل البسيط بنجاح!")
    print(f"\n📁 مجلد النظام: {system_folder}")
    print("\n📋 الملفات المُنشأة:")
    print("1. مجلد 'نظام المحاسبة' على سطح المكتب")
    print("2. اختصار '🏢 نظام المحاسبة.bat' على سطح المكتب")
    
    print("\n🚀 كيفية الاستخدام:")
    print("1. انقر مرتين على اختصار '🏢 نظام المحاسبة' على سطح المكتب")
    print("2. في المرة الأولى: شغل 'تثبيت المكتبات.bat'")
    print("3. بعد ذلك: شغل 'تشغيل النظام.bat'")
    
    print("\n✅ هذا الحل يعمل بدون مشاكل!")
    print("💡 لا يحتاج ملف تنفيذي معقد")
    print("🔧 سهل الصيانة والتحديث")

if __name__ == "__main__":
    main()
