using System;
using System.Drawing;
using System.Windows.Forms;

namespace WorkingAccounting
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }
    }

    public partial class MainForm : Form
    {
        private string currentUser = "";
        
        public MainForm()
        {
            InitializeComponent();
            ShowLoginScreen();
        }
        
        private void InitializeComponent()
        {
            this.Text = "نظام المحاسبة المتكامل - Modern Accounting System";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(20, 30, 48); // خلفية داكنة أنيقة
            this.Font = new Font("Segoe UI", 10F);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
        }
        
        private void ShowLoginScreen()
        {
            this.Controls.Clear();
            
            // بطاقة تسجيل الدخول
            Panel loginPanel = new Panel
            {
                Size = new Size(450, 550),
                BackColor = Color.FromArgb(30, 41, 59),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            loginPanel.Location = new Point(
                (this.ClientSize.Width - loginPanel.Width) / 2,
                (this.ClientSize.Height - loginPanel.Height) / 2
            );
            
            // العنوان
            Label titleLabel = new Label
            {
                Text = "🏢 نظام المحاسبة المتكامل",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(59, 130, 246),
                Size = new Size(420, 50),
                Location = new Point(15, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label subtitleLabel = new Label
            {
                Text = "تطبيق محاسبي متطور وعصري",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.FromArgb(156, 163, 175),
                Size = new Size(420, 30),
                Location = new Point(15, 85),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // حقل اسم المستخدم
            Label usernameLabel = new Label
            {
                Text = "👤 اسم المستخدم:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(150, 25),
                Location = new Point(30, 150)
            };
            
            TextBox usernameBox = new TextBox
            {
                Font = new Font("Segoe UI", 12F),
                Size = new Size(380, 35),
                Location = new Point(30, 180),
                Text = "admin",
                BackColor = Color.FromArgb(51, 65, 85),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // حقل كلمة المرور
            Label passwordLabel = new Label
            {
                Text = "🔒 كلمة المرور:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(150, 25),
                Location = new Point(30, 240)
            };
            
            TextBox passwordBox = new TextBox
            {
                Font = new Font("Segoe UI", 12F),
                Size = new Size(380, 35),
                Location = new Point(30, 270),
                Text = "admin123",
                UseSystemPasswordChar = true,
                BackColor = Color.FromArgb(51, 65, 85),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // زر تسجيل الدخول
            Button loginButton = new Button
            {
                Text = "🚀 تسجيل الدخول",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Size = new Size(380, 50),
                Location = new Point(30, 330),
                BackColor = Color.FromArgb(59, 130, 246),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            
            loginButton.Click += (sender, e) => {
                string username = usernameBox.Text.Trim();
                string password = passwordBox.Text.Trim();
                
                if (username == "admin" && password == "admin123")
                {
                    currentUser = "المدير العام 👑";
                    ShowMainDashboard();
                }
                else if (username == "accountant" && password == "acc123")
                {
                    currentUser = "المحاسب الرئيسي 📊";
                    ShowMainDashboard();
                }
                else if (username == "manager" && password == "mgr123")
                {
                    currentUser = "مدير المبيعات 🏪";
                    ShowMainDashboard();
                }
                else
                {
                    MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };
            
            // معلومات تسجيل الدخول
            Label infoLabel = new Label
            {
                Text = "🔑 بيانات تسجيل الدخول:\n👑 المدير: admin / admin123\n📊 المحاسب: accountant / acc123\n🏪 مدير المبيعات: manager / mgr123",
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(156, 163, 175),
                Size = new Size(380, 100),
                Location = new Point(30, 400),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // إضافة العناصر
            loginPanel.Controls.AddRange(new Control[] {
                titleLabel, subtitleLabel, usernameLabel, usernameBox,
                passwordLabel, passwordBox, loginButton, infoLabel
            });
            
            this.Controls.Add(loginPanel);
            
            // ربط Enter بتسجيل الدخول
            passwordBox.KeyPress += (sender, e) => {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    loginButton.PerformClick();
                }
            };
        }
        
        private void ShowMainDashboard()
        {
            this.Controls.Clear();
            this.BackColor = Color.FromArgb(15, 23, 42);
            
            // شريط علوي
            Panel topBar = new Panel
            {
                Size = new Size(this.ClientSize.Width, 60),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(30, 41, 59)
            };
            
            Label welcomeLabel = new Label
            {
                Text = $"مرحباً، {currentUser}",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 15),
                AutoSize = true
            };
            
            Button logoutButton = new Button
            {
                Text = "🚪 تسجيل الخروج",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(120, 35),
                Location = new Point(this.ClientSize.Width - 140, 12),
                BackColor = Color.FromArgb(239, 68, 68),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            logoutButton.Click += (s, e) => ShowLoginScreen();
            
            topBar.Controls.AddRange(new Control[] { welcomeLabel, logoutButton });
            
            // المحتوى الرئيسي
            Panel contentPanel = new Panel
            {
                Size = new Size(this.ClientSize.Width - 40, this.ClientSize.Height - 100),
                Location = new Point(20, 80),
                BackColor = Color.FromArgb(30, 41, 59)
            };
            
            Label dashboardTitle = new Label
            {
                Text = "📊 لوحة التحكم الرئيسية",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(30, 30),
                AutoSize = true
            };
            
            Label successMessage = new Label
            {
                Text = "🎉 تم تشغيل النظام بنجاح! النظام جاهز للاستخدام.",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(34, 197, 94),
                Location = new Point(30, 80),
                Size = new Size(800, 40)
            };
            
            // بطاقات الإحصائيات
            var stats = new[]
            {
                new { Title = "العملاء", Value = "156", Icon = "👥", Color = Color.FromArgb(59, 130, 246) },
                new { Title = "المنتجات", Value = "89", Icon = "📦", Color = Color.FromArgb(16, 185, 129) },
                new { Title = "الفواتير", Value = "342", Icon = "🧾", Color = Color.FromArgb(245, 158, 11) },
                new { Title = "الأرباح", Value = "59,700 ر.س", Icon = "💰", Color = Color.FromArgb(239, 68, 68) }
            };
            
            for (int i = 0; i < stats.Length; i++)
            {
                Panel statCard = new Panel
                {
                    Size = new Size(250, 120),
                    Location = new Point(30 + (i * 270), 150),
                    BackColor = stats[i].Color
                };
                
                Label iconLabel = new Label
                {
                    Text = stats[i].Icon,
                    Font = new Font("Segoe UI Emoji", 24F),
                    ForeColor = Color.White,
                    Size = new Size(60, 40),
                    Location = new Point(15, 20),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                
                Label valueLabel = new Label
                {
                    Text = stats[i].Value,
                    Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                    ForeColor = Color.White,
                    Size = new Size(160, 30),
                    Location = new Point(80, 25),
                };
                
                Label titleLabel = new Label
                {
                    Text = stats[i].Title,
                    Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                    ForeColor = Color.White,
                    Size = new Size(220, 25),
                    Location = new Point(15, 70),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                
                statCard.Controls.AddRange(new Control[] { iconLabel, valueLabel, titleLabel });
                contentPanel.Controls.Add(statCard);
            }
            
            contentPanel.Controls.AddRange(new Control[] { dashboardTitle, successMessage });
            
            this.Controls.AddRange(new Control[] { topBar, contentPanel });
        }
    }
}
