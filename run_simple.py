#!/usr/bin/env python3
"""
تشغيل النظام المحاسبي - نسخة مبسطة مع تسجيل الدخول
Run Accounting System - Simple Version with Login
"""

from flask import Flask, render_template, redirect, url_for, request, flash, session
from flask_sqlalchemy import SQLAlchemy
import os

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات
app.config['SECRET_KEY'] = 'demo-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_accounting.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# نموذج مستخدم مبسط
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

# المسارات
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # التحقق من بيانات تسجيل الدخول
        valid_users = {
            'admin': {'password': 'admin123', 'name': 'أحمد المدير'},
            'accountant': {'password': 'acc123', 'name': 'فاطمة المحاسبة'},
            'employee': {'password': 'emp123', 'name': 'محمد الموظف'}
        }
        
        if username in valid_users and valid_users[username]['password'] == password:
            session['user_id'] = username
            session['user_name'] = valid_users[username]['name']
            flash(f'مرحباً {valid_users[username]["name"]}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('simple_login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # بيانات تجريبية للعرض
    stats = {
        'total_invoices': 45,
        'total_revenue': 125000,
        'total_expenses': 85000,
        'net_profit': 40000
    }
    
    recent_activities = [
        {
            'icon': 'fas fa-file-invoice',
            'title': 'فاتورة جديدة #INV-2024-0001',
            'description': 'للعميل: شركة التقنية المتقدمة',
            'time_ago': 'منذ ساعتين'
        },
        {
            'icon': 'fas fa-receipt',
            'title': 'مصروف جديد #EXP-2024-0015',
            'description': 'إيجار المكتب - شهر ديسمبر',
            'time_ago': 'منذ 4 ساعات'
        },
        {
            'icon': 'fas fa-users',
            'title': 'كشف راتب جديد',
            'description': 'رواتب شهر ديسمبر 2024',
            'time_ago': 'منذ يوم واحد'
        }
    ]
    
    pending_tasks = {
        'overdue_invoices': 3,
        'pending_approvals': 5,
        'completed_today': 8,
        'due_this_week': 12
    }
    
    return render_template('simple_dashboard.html', 
                         stats=stats,
                         recent_activities=recent_activities,
                         pending_tasks=pending_tasks,
                         user_name=session.get('user_name', 'مستخدم'))

@app.route('/invoices')
def invoices():
    """صفحة الفواتير"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # بيانات فواتير تجريبية
    invoices = [
        {
            'id': 1,
            'invoice_number': 'INV-2024-0001',
            'customer_name': 'شركة التقنية المتقدمة',
            'total_amount': 25000,
            'status': 'paid',
            'status_text': 'مدفوعة',
            'status_color': 'success',
            'issue_date': '2024-12-01',
            'due_date': '2024-12-31'
        },
        {
            'id': 2,
            'invoice_number': 'INV-2024-0002',
            'customer_name': 'مؤسسة الحلول الذكية',
            'total_amount': 18500,
            'status': 'sent',
            'status_text': 'مرسلة',
            'status_color': 'info',
            'issue_date': '2024-12-15',
            'due_date': '2025-01-15'
        },
        {
            'id': 3,
            'invoice_number': 'INV-2024-0003',
            'customer_name': 'شركة الابتكار التقني',
            'total_amount': 32000,
            'status': 'overdue',
            'status_text': 'متأخرة',
            'status_color': 'danger',
            'issue_date': '2024-11-20',
            'due_date': '2024-12-20'
        }
    ]
    
    stats = {
        'total': 45,
        'draft': 5,
        'sent': 12,
        'paid': 25,
        'overdue': 3
    }
    
    return render_template('simple_invoices.html', 
                         invoices=invoices, 
                         stats=stats,
                         user_name=session.get('user_name', 'مستخدم'))

@app.route('/expenses')
def expenses():
    """صفحة المصروفات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    expenses = [
        {
            'id': 1,
            'reference_number': 'EXP-2024-0015',
            'title': 'إيجار المكتب - شهر ديسمبر',
            'amount': 12000,
            'category': 'الإيجار',
            'status': 'paid',
            'status_text': 'مدفوع',
            'status_color': 'success',
            'expense_date': '2024-12-01'
        },
        {
            'id': 2,
            'reference_number': 'EXP-2024-0016',
            'title': 'فاتورة الكهرباء',
            'amount': 850,
            'category': 'المرافق',
            'status': 'approved',
            'status_text': 'موافق عليه',
            'status_color': 'info',
            'expense_date': '2024-12-28'
        },
        {
            'id': 3,
            'reference_number': 'EXP-2024-0017',
            'title': 'مستلزمات مكتبية',
            'amount': 450,
            'category': 'المكتبية',
            'status': 'pending',
            'status_text': 'في انتظار الموافقة',
            'status_color': 'warning',
            'expense_date': '2024-12-28'
        }
    ]
    
    return render_template('simple_expenses.html', 
                         expenses=expenses,
                         user_name=session.get('user_name', 'مستخدم'))

@app.route('/home')
def home():
    """الصفحة الرئيسية للعرض"""
    return render_template('demo_index.html')

if __name__ == '__main__':
    print("🚀 تم تشغيل النظام المحاسبي مع تسجيل الدخول!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123") 
    print("   الموظف: employee / emp123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
