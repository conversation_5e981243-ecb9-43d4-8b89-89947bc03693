#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء نظام المحاسبة كملف تنفيذي بأسماء إنجليزية
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    print("🏗️  Building Accounting System Executable (English Names)")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    if not Path('accounting_system_db.py').exists():
        print("❌ Error: Main system file not found")
        return
    
    # إنشاء ملف .spec بأسماء إنجليزية
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['accounting_system_db.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('accounting_system.db', '.'),
        ('remote_access_guide.md', '.'),
    ],
    hiddenimports=[
        'flask',
        'sqlite3',
        'datetime',
        'hashlib',
        'secrets',
        'json',
        'zipfile',
        'shutil',
        'psutil',
        'platform'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AccountingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('accounting_english.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Created .spec file")
    
    # إنشاء ملف بدء التشغيل
    startup_content = '''@echo off
title Accounting System - Starting...
echo.
echo ================================================
echo           Integrated Accounting System
echo ================================================
echo.
echo Starting the system...
echo Wait until you see "Running on http://127.0.0.1:5000"
echo Then open your browser and go to: http://localhost:5000
echo.

REM Start the system
AccountingSystem.exe

REM When system closes
echo.
echo System closed
echo Press any key to exit...
pause > nul
'''
    
    with open('StartSystem.bat', 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ Created startup script")
    
    # إنشاء ملف التعليمات
    readme_content = '''# Integrated Accounting System

## 🚀 How to Run

### Method 1 (Easiest):
1. Double-click on "StartSystem.bat"
2. Wait until you see "Running on http://127.0.0.1:5000"
3. Open your browser and go to: http://localhost:5000

### Method 2:
1. Double-click on "AccountingSystem.exe"
2. Open your browser and go to: http://localhost:5000

## 🔑 Login Credentials

### Admin:
- Username: admin
- Password: admin123

### Accountant:
- Username: accountant
- Password: acc123

### Store Manager:
- Username: manager
- Password: mgr123

## 📁 Important Files

- AccountingSystem.exe - Main executable file
- accounting_system.db - Database file
- StartSystem.bat - Easy startup script
- README.txt - This file

## ⚠️ Important Warnings

1. Do not delete the database file (accounting_system.db)
2. Make regular backups of the database
3. Make sure to close the system properly before shutting down the computer
4. Do not run more than one instance of the system at the same time

## 🔧 Troubleshooting

### Problem: System doesn't open in browser
Solution: Make sure port 5000 is not used by another program

### Problem: Error message when starting
Solution: Run the system as administrator (Run as Administrator)

### Problem: Data loss
Solution: Restore from database backup

---
Developed by Professional Development Team
'''
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Created README file")
    
    # بناء الملف التنفيذي
    print("🔨 Building executable...")
    print("This may take several minutes...")
    
    try:
        result = subprocess.run([
            'py', '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'accounting_english.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Executable built successfully!")
            
            # نسخ الملفات المهمة إلى مجلد dist
            dist_path = Path('dist')
            if dist_path.exists():
                files_to_copy = [
                    'accounting_system.db',
                    'StartSystem.bat',
                    'README.txt',
                    'remote_access_guide.md'
                ]
                
                for file_name in files_to_copy:
                    if Path(file_name).exists():
                        shutil.copy2(file_name, dist_path)
                        print(f"📄 Copied {file_name}")
                
                print(f"\n📁 Files ready in folder: {dist_path.absolute()}")
                print("🎉 You can now distribute the contents of the dist folder")
                print("\n📋 Folder contents:")
                for item in dist_path.iterdir():
                    print(f"  - {item.name}")
            
        else:
            print("❌ Error during build:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n🎯 Build process completed!")

if __name__ == "__main__":
    main()
