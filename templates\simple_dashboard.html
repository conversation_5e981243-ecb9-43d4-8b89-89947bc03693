{% extends "demo_base.html" %}

{% block title %}لوحة التحكم - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-gradient text-white" style="background: linear-gradient(135deg, #10b981, #059669);">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="fw-bold mb-2">مرحباً {{ user_name }}</h2>
                        <p class="mb-0 opacity-75">إليك نظرة سريعة على أداء شركتك اليوم</p>
                        <div class="mt-3">
                            <a href="{{ url_for('logout') }}" class="btn btn-outline-light">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="stats-number">{{ stats.total_invoices }}</div>
            <div class="text-muted">إجمالي الفواتير</div>
            <small class="text-success">
                <i class="fas fa-arrow-up me-1"></i>
                +12% من الشهر الماضي
            </small>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stats-number">{{ "{:,.0f}".format(stats.total_revenue) }}</div>
            <div class="text-muted">إجمالي الإيرادات (ر.س)</div>
            <small class="text-success">
                <i class="fas fa-arrow-up me-1"></i>
                +8% من الشهر الماضي
            </small>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-receipt"></i>
            </div>
            <div class="stats-number">{{ "{:,.0f}".format(stats.total_expenses) }}</div>
            <div class="text-muted">إجمالي المصروفات (ر.س)</div>
            <small class="text-danger">
                <i class="fas fa-arrow-up me-1"></i>
                +5% من الشهر الماضي
            </small>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon danger">
                <i class="fas fa-chart-pie"></i>
            </div>
            <div class="stats-number">{{ "{:,.0f}".format(stats.net_profit) }}</div>
            <div class="text-muted">صافي الربح (ر.س)</div>
            <small class="text-success">
                <i class="fas fa-arrow-up me-1"></i>
                +15% من الشهر الماضي
            </small>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-xl-8 mb-3">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="fw-bold mb-0">الإيرادات والمصروفات الشهرية</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active">6 أشهر</button>
                        <button class="btn btn-outline-primary">سنة</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-4 mb-3">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="fw-bold mb-0">توزيع المصروفات</h5>
            </div>
            <div class="card-body">
                <canvas id="expenseChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Activities and Tasks -->
<div class="row">
    <div class="col-xl-6 mb-3">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2 text-primary"></i>
                    الأنشطة الحديثة
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item border-0 py-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="{{ activity.icon }}"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ activity.title }}</h6>
                                <p class="text-muted mb-1 small">{{ activity.description }}</p>
                                <small class="text-muted">{{ activity.time_ago }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 mb-3">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2 text-primary"></i>
                    المهام المعلقة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-warning mb-2">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-warning">{{ pending_tasks.overdue_invoices }}</h4>
                            <small class="text-muted">فواتير متأخرة</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-info mb-2">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-info">{{ pending_tasks.pending_approvals }}</h4>
                            <small class="text-muted">في انتظار الموافقة</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-success mb-2">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-success">{{ pending_tasks.completed_today }}</h4>
                            <small class="text-muted">مكتملة اليوم</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 text-center bg-light">
                            <div class="text-primary mb-2">
                                <i class="fas fa-calendar-alt fa-2x"></i>
                            </div>
                            <h4 class="fw-bold text-primary">{{ pending_tasks.due_this_week }}</h4>
                            <small class="text-muted">مستحقة هذا الأسبوع</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="{{ url_for('invoices') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                            إدارة الفواتير
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('expenses') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                            إدارة المصروفات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info w-100 py-3" onclick="showDemo('reports')">
                            <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                            التقارير المالية
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning w-100 py-3" onclick="showDemo('settings')">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'الإيرادات',
                data: [18000, 22000, 19000, 28000, 25000, 32000],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'المصروفات',
                data: [12000, 15000, 13000, 18000, 16000, 20000],
                borderColor: '#059669',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });

    // Expense Chart
    const expenseCtx = document.getElementById('expenseChart').getContext('2d');
    new Chart(expenseCtx, {
        type: 'doughnut',
        data: {
            labels: ['الإيجار', 'الرواتب', 'المرافق', 'التسويق', 'أخرى'],
            datasets: [{
                data: [35, 30, 15, 12, 8],
                backgroundColor: [
                    '#10b981',
                    '#22c55e',
                    '#84cc16',
                    '#059669',
                    '#047857'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});

function showDemo(type) {
    const messages = {
        'reports': 'في النسخة الكاملة، ستتمكن من إنشاء تقارير مالية شاملة ومفصلة!',
        'settings': 'في النسخة الكاملة، ستتمكن من إدارة إعدادات الشركة والنظام!'
    };
    
    alert(messages[type] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
}
</script>
{% endblock %}
