using System;
using System.Data.SQLite;
using System.Drawing;
using System.Windows.Forms;

namespace AccountingSystem
{
    public partial class CustomerForm : Form
    {
        private SQLiteConnection connection;
        private int? customerId;
        
        private TextBox nameTextBox;
        private TextBox emailTextBox;
        private TextBox phoneTextBox;
        private TextBox addressTextBox;
        
        public CustomerForm(SQLiteConnection conn, int? id = null)
        {
            connection = conn;
            customerId = id;
            InitializeComponent();
            
            if (customerId.HasValue)
            {
                LoadCustomerData();
            }
        }
        
        private void InitializeComponent()
        {
            this.Text = customerId.HasValue ? "تعديل عميل" : "إضافة عميل جديد";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // عنوان النافذة
            Label titleLabel = new Label
            {
                Text = "بيانات العميل",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };
            
            // حقول النموذج
            int yPos = 60;
            int spacing = 50;
            
            // اسم العميل
            Label nameLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            nameTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // البريد الإلكتروني
            Label emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            emailTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // رقم الهاتف
            Label phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            phoneTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // العنوان
            Label addressLabel = new Label
            {
                Text = "العنوان:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            addressTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 60),
                Font = new Font("Segoe UI", 9F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            
            yPos += 80;
            
            // أزرار الحفظ والإلغاء
            Button saveButton = new Button
            {
                Text = "حفظ",
                Size = new Size(80, 30),
                Location = new Point(330, yPos),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;
            
            Button cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 30),
                Location = new Point(240, yPos),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;
            
            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                titleLabel, nameLabel, nameTextBox, emailLabel, emailTextBox,
                phoneLabel, phoneTextBox, addressLabel, addressTextBox,
                saveButton, cancelButton
            });
        }
        
        private void LoadCustomerData()
        {
            string query = "SELECT * FROM customers WHERE id = @id";
            using (var command = new SQLiteCommand(query, connection))
            {
                command.Parameters.AddWithValue("@id", customerId.Value);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        nameTextBox.Text = reader.GetString("name");
                        emailTextBox.Text = reader.IsDBNull("email") ? "" : reader.GetString("email");
                        phoneTextBox.Text = reader.IsDBNull("phone") ? "" : reader.GetString("phone");
                        addressTextBox.Text = reader.IsDBNull("address") ? "" : reader.GetString("address");
                    }
                }
            }
        }
        
        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من البيانات
                if (string.IsNullOrWhiteSpace(nameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nameTextBox.Focus();
                    return;
                }
                
                // التحقق من صحة البريد الإلكتروني (اختياري)
                if (!string.IsNullOrWhiteSpace(emailTextBox.Text) && !IsValidEmail(emailTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    emailTextBox.Focus();
                    return;
                }
                
                // حفظ البيانات
                string query;
                if (customerId.HasValue)
                {
                    // تحديث العميل
                    query = @"UPDATE customers SET name = @name, email = @email, 
                             phone = @phone, address = @address WHERE id = @id";
                }
                else
                {
                    // إضافة عميل جديد
                    query = @"INSERT INTO customers (name, email, phone, address) 
                             VALUES (@name, @email, @phone, @address)";
                }
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@name", nameTextBox.Text.Trim());
                    command.Parameters.AddWithValue("@email", string.IsNullOrWhiteSpace(emailTextBox.Text) ? (object)DBNull.Value : emailTextBox.Text.Trim());
                    command.Parameters.AddWithValue("@phone", string.IsNullOrWhiteSpace(phoneTextBox.Text) ? (object)DBNull.Value : phoneTextBox.Text.Trim());
                    command.Parameters.AddWithValue("@address", string.IsNullOrWhiteSpace(addressTextBox.Text) ? (object)DBNull.Value : addressTextBox.Text.Trim());
                    
                    if (customerId.HasValue)
                    {
                        command.Parameters.AddWithValue("@id", customerId.Value);
                    }
                    
                    command.ExecuteNonQuery();
                }
                
                MessageBox.Show(customerId.HasValue ? "تم تحديث العميل بنجاح" : "تم إضافة العميل بنجاح", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
