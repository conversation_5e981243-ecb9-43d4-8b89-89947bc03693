# نظام المحاسبة - C# Desktop Application

## 🖥️ تطبيق سطح مكتب حقيقي بـ C#

تم تحويل النظام بالكامل إلى C# مع الحفاظ على نفس التصميم والوظائف!

## 📋 المتطلبات

### البرامج المطلوبة:
- **Windows 7** أو أحدث
- **.NET 6.0** أو أحدث ([تحميل من هنا](https://dotnet.microsoft.com/download))
- **Visual Studio 2022** (اختياري للتطوير)

### المكتبات المستخدمة:
- **Windows Forms** - للواجهة الرسومية
- **SQLite** - لقاعدة البيانات
- **System.Data.SQLite** - للتعامل مع قاعدة البيانات

## 🚀 كيفية التشغيل

### الطريقة الأولى (الأسهل):
```bash
# انقر مرتين على:
build_and_run.bat
```

### الطريقة الثانية (يدوياً):
```bash
# 1. بناء المشروع
dotnet build --configuration Release

# 2. تشغيل التطبيق
dotnet run --configuration Release
```

### الطريقة الثالثة (Visual Studio):
1. افتح ملف `AccountingSystem.csproj` في Visual Studio
2. اضغط F5 أو اختر "Start Debugging"

## 🔑 بيانات تسجيل الدخول

| المستخدم | اسم المستخدم | كلمة المرور |
|-----------|---------------|-------------|
| 👤 المدير العام | `admin` | `admin123` |
| 📊 المحاسب | `accountant` | `acc123` |
| 🏪 مدير المتجر | `manager` | `mgr123` |

## 📁 هيكل المشروع

```
AccountingSystem/
├── AccountingSystem.cs      # النافذة الرئيسية
├── ProductForm.cs          # نموذج المنتجات
├── CustomerForm.cs         # نموذج العملاء
├── Program.cs              # نقطة البداية
├── AccountingSystem.csproj # ملف المشروع
├── build_and_run.bat      # ملف التشغيل
└── README_CSharp.md       # هذا الملف
```

## 💻 الواجهة والتصميم

### ✅ تم الحفاظ على:
- **نفس الألوان** والتصميم الأصلي
- **نفس التخطيط** والترتيب
- **نفس الوظائف** والميزات
- **الواجهة العربية** الكاملة

### 🎨 الألوان المستخدمة:
- **الأزرق الرئيسي:** `#2E86AB` (46, 134, 171)
- **الأخضر:** `#3A7D44` (58, 125, 68)
- **البرتقالي:** `#F39237` (243, 146, 55)
- **الأحمر:** `#DC3545` (220, 53, 69)

## 🔧 الوظائف المتاحة

### ✅ مكتملة:
- 🔐 **تسجيل الدخول الآمن**
- 📊 **لوحة التحكم** مع الإحصائيات
- 📦 **إدارة المنتجات** (إضافة، تعديل، عرض، حذف)
- 👥 **إدارة العملاء** (إضافة، تعديل، عرض، حذف)
- 🎨 **واجهة احترافية** مع الألوان والتصميم الأصلي

### 🔄 قيد التطوير:
- 🧾 **إدارة الفواتير**
- 📈 **التقارير المتقدمة**
- 💰 **إدارة المدفوعات**
- 📋 **سجل التدقيق**

## 🏗️ البنية التقنية

### **نوع التطبيق:**
- 🖥️ **Windows Forms Application**
- 💻 **Desktop Native Application**
- 🗄️ **SQLite Database**

### **المميزات التقنية:**
- ⚡ **أداء عالي** - تطبيق native
- 🔒 **أمان محلي** - بيانات على الجهاز
- 📱 **واجهة متجاوبة** - تتكيف مع الشاشة
- 🎯 **سهولة الاستخدام** - واجهة Windows مألوفة

## 🆚 مقارنة مع النسخة الويب

| الميزة | النسخة الويب | نسخة C# |
|--------|-------------|---------|
| **نوع الواجهة** | متصفح | Windows Forms |
| **الأداء** | متوسط | عالي جداً |
| **الاستجابة** | يعتمد على الإنترنت | فوري |
| **التثبيت** | لا يحتاج | يحتاج .NET |
| **الأمان** | جيد | ممتاز |
| **سهولة التطوير** | سهل | متوسط |

## 🔧 التطوير والتخصيص

### إضافة ميزات جديدة:
1. أضف النموذج الجديد (مثل `InvoiceForm.cs`)
2. أضف الوظائف في `AccountingSystem.cs`
3. أضف القوائم في `CreateMenuBar()`
4. أضف الجداول في قاعدة البيانات

### تخصيص التصميم:
```csharp
// تغيير الألوان
Color primaryColor = Color.FromArgb(46, 134, 171);
Color successColor = Color.FromArgb(58, 125, 68);

// تغيير الخطوط
Font arabicFont = new Font("Segoe UI", 9F);
Font titleFont = new Font("Segoe UI", 16F, FontStyle.Bold);
```

## 📦 إنشاء ملف تنفيذي

### لإنشاء ملف .exe:
```bash
# بناء للنشر
dotnet publish -c Release -r win-x64 --self-contained true

# الملف التنفيذي سيكون في:
# bin/Release/net6.0-windows/win-x64/publish/AccountingSystem.exe
```

## ⚠️ استكشاف الأخطاء

### المشكلة: .NET غير موجود
**الحل:** تثبيت .NET 6.0 من الموقع الرسمي

### المشكلة: خطأ في قاعدة البيانات
**الحل:** تأكد من وجود صلاحيات الكتابة في المجلد

### المشكلة: الخطوط لا تظهر بشكل صحيح
**الحل:** تأكد من وجود خط Segoe UI على النظام

## 🎉 الخلاصة

**تم تحويل النظام بنجاح إلى C# مع الحفاظ على:**
- ✅ **نفس التصميم** والألوان
- ✅ **نفس الوظائف** والميزات  
- ✅ **الواجهة العربية** الكاملة
- ✅ **سهولة الاستخدام**

**الآن لديك نظام محاسبي حقيقي بـ C# يعمل على Windows!** 🚀

---
**تم التطوير بواسطة فريق متخصص في C# و .NET**
