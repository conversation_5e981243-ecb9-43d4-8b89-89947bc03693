using System;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace AccountingSystem
{
    public partial class MainForm : Form
    {
        private SQLiteConnection connection;
        private User currentUser;
        private Panel contentPanel;
        private Label welcomeLabel;
        
        public MainForm()
        {
            InitializeComponent();
            InitializeDatabase();
            ShowLoginInterface();
        }
        
        private void InitializeDatabase()
        {
            try
            {
                string connectionString = "Data Source=accounting_vs.db;Version=3;";
                connection = new SQLiteConnection(connectionString);
                connection.Open();
                
                CreateTables();
                InsertSampleData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void CreateTables()
        {
            string[] createTableQueries = {
                @"CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    sku TEXT UNIQUE,
                    price REAL NOT NULL,
                    cost REAL NOT NULL,
                    stock_quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )"
            };
            
            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }
        
        private void InsertSampleData()
        {
            // التحقق من وجود المستخدمين
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM users", connection))
            {
                int userCount = Convert.ToInt32(command.ExecuteScalar());
                
                if (userCount == 0)
                {
                    // إضافة مستخدمين افتراضيين
                    string[] userInserts = {
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('admin', '{HashPassword("admin123")}', 'المدير العام', 'admin', '<EMAIL>')",
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('accountant', '{HashPassword("acc123")}', 'المحاسب الرئيسي', 'accountant', '<EMAIL>')",
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('manager', '{HashPassword("mgr123")}', 'مدير المبيعات', 'manager', '<EMAIL>')"
                    };
                    
                    foreach (string insert in userInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                    
                    // إضافة منتجات تجريبية
                    string[] productInserts = {
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('لابتوب Dell', 'DELL001', 2500.00, 2000.00, 10, 2, 'لابتوب Dell Inspiron 15')",
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('ماوس لاسلكي', 'MOUSE001', 50.00, 30.00, 25, 5, 'ماوس لاسلكي عالي الجودة')",
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('كيبورد ميكانيكي', 'KB001', 150.00, 100.00, 15, 3, 'كيبورد ميكانيكي للألعاب')"
                    };
                    
                    foreach (string insert in productInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                    
                    // إضافة عملاء تجريبيين
                    string[] customerInserts = {
                        "INSERT INTO customers (name, email, phone, address) VALUES ('شركة التقنية المتقدمة', '<EMAIL>', '0501234567', 'الرياض، المملكة العربية السعودية')",
                        "INSERT INTO customers (name, email, phone, address) VALUES ('مؤسسة الأعمال الذكية', '<EMAIL>', '0507654321', 'جدة، المملكة العربية السعودية')"
                    };
                    
                    foreach (string insert in customerInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                }
            }
        }
        
        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        
        private void ShowLoginInterface()
        {
            // مسح المحتوى الحالي
            this.Controls.Clear();
            
            // إنشاء panel تسجيل الدخول
            Panel loginPanel = new Panel
            {
                Size = new Size(400, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // توسيط panel
            loginPanel.Location = new Point(
                (this.ClientSize.Width - loginPanel.Width) / 2,
                (this.ClientSize.Height - loginPanel.Height) / 2
            );
            
            // عنوان النظام
            Label titleLabel = new Label
            {
                Text = "نظام المحاسبة المتكامل",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Size = new Size(350, 40),
                Location = new Point(25, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label subtitleLabel = new Label
            {
                Text = "Visual Studio 2022 - C# Desktop",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray,
                Size = new Size(350, 25),
                Location = new Point(25, 75),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // حقول تسجيل الدخول
            Label usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 25),
                Location = new Point(30, 130)
            };
            
            TextBox usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(300, 25),
                Location = new Point(30, 155),
                Name = "usernameTextBox"
            };
            
            Label passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 25),
                Location = new Point(30, 190)
            };
            
            TextBox passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(300, 25),
                Location = new Point(30, 215),
                UseSystemPasswordChar = true,
                Name = "passwordTextBox"
            };
            
            // زر تسجيل الدخول
            Button loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(300, 35),
                Location = new Point(30, 260),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            loginButton.Click += (sender, e) => Login(usernameTextBox.Text, passwordTextBox.Text);
            
            // معلومات تسجيل الدخول
            Label infoLabel = new Label
            {
                Text = "بيانات تسجيل الدخول:\nالمدير: admin / admin123\nالمحاسب: accountant / acc123\nالمدير: manager / mgr123",
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Gray,
                Size = new Size(300, 80),
                Location = new Point(30, 320)
            };
            
            // إضافة العناصر للـ panel
            loginPanel.Controls.AddRange(new Control[] {
                titleLabel, subtitleLabel, usernameLabel, usernameTextBox,
                passwordLabel, passwordTextBox, loginButton, infoLabel
            });
            
            // إضافة panel للنافذة
            this.Controls.Add(loginPanel);
            
            // ربط Enter بتسجيل الدخول
            passwordTextBox.KeyPress += (sender, e) => {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    Login(usernameTextBox.Text, passwordTextBox.Text);
                }
            };
        }
        
        private void Login(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            string hashedPassword = HashPassword(password);
            string query = "SELECT * FROM users WHERE username = @username AND password = @password";
            
            using (var command = new SQLiteCommand(query, connection))
            {
                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@password", hashedPassword);
                
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        currentUser = new User
                        {
                            Id = reader.GetInt32("id"),
                            Username = reader.GetString("username"),
                            FullName = reader.GetString("full_name"),
                            Role = reader.GetString("role"),
                            Email = reader.IsDBNull("email") ? "" : reader.GetString("email")
                        };
                        
                        ShowMainInterface();
                    }
                    else
                    {
                        MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        
        private void ShowMainInterface()
        {
            // مسح المحتوى الحالي
            this.Controls.Clear();
            
            // إنشاء شريط القوائم
            CreateMenuBar();
            
            // إنشاء شريط المعلومات العلوي
            CreateTopInfoBar();
            
            // إنشاء منطقة المحتوى
            contentPanel = new Panel
            {
                Location = new Point(10, 80),
                Size = new Size(this.ClientSize.Width - 20, this.ClientSize.Height - 90),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            this.Controls.Add(contentPanel);
            
            // عرض لوحة التحكم افتراضياً
            ShowDashboard();
        }
        
        private void CreateMenuBar()
        {
            MenuStrip menuStrip = new MenuStrip
            {
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F)
            };
            
            // قائمة النظام
            ToolStripMenuItem systemMenu = new ToolStripMenuItem("النظام");
            systemMenu.DropDownItems.Add("لوحة التحكم", null, (s, e) => ShowDashboard());
            systemMenu.DropDownItems.Add(new ToolStripSeparator());
            systemMenu.DropDownItems.Add("تسجيل الخروج", null, (s, e) => Logout());
            systemMenu.DropDownItems.Add("إغلاق", null, (s, e) => this.Close());
            
            // قائمة المنتجات
            ToolStripMenuItem productsMenu = new ToolStripMenuItem("المنتجات");
            productsMenu.DropDownItems.Add("عرض المنتجات", null, (s, e) => ShowProducts());
            productsMenu.DropDownItems.Add("إضافة منتج", null, (s, e) => AddProduct());
            
            // قائمة العملاء
            ToolStripMenuItem customersMenu = new ToolStripMenuItem("العملاء");
            customersMenu.DropDownItems.Add("عرض العملاء", null, (s, e) => ShowCustomers());
            customersMenu.DropDownItems.Add("إضافة عميل", null, (s, e) => AddCustomer());
            
            // قائمة حول
            ToolStripMenuItem aboutMenu = new ToolStripMenuItem("حول");
            aboutMenu.DropDownItems.Add("حول النظام", null, (s, e) => ShowAbout());
            
            menuStrip.Items.AddRange(new ToolStripItem[] {
                systemMenu, productsMenu, customersMenu, aboutMenu
            });
            
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }
        
        private void CreateTopInfoBar()
        {
            Panel infoPanel = new Panel
            {
                Location = new Point(10, 30),
                Size = new Size(this.ClientSize.Width - 20, 45),
                BackColor = Color.FromArgb(46, 134, 171)
            };
            
            welcomeLabel = new Label
            {
                Text = $"مرحباً، {currentUser.FullName}",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 12),
                AutoSize = true
            };
            
            Label timeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(infoPanel.Width - 150, 15),
                AutoSize = true
            };
            
            infoPanel.Controls.AddRange(new Control[] { welcomeLabel, timeLabel });
            this.Controls.Add(infoPanel);
        }
        
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();

            Label titleLabel = new Label
            {
                Text = "لوحة التحكم - Visual Studio 2022",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            Label descLabel = new Label
            {
                Text = "تم إنشاء النظام بنجاح في Visual Studio 2022 مع الحفاظ على التصميم الأصلي",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray,
                Location = new Point(20, 50),
                AutoSize = true
            };

            // بطاقات الإحصائيات
            var stats = new[]
            {
                new { Title = "النظام", Value = "يعمل بنجاح", Color = Color.FromArgb(58, 125, 68) },
                new { Title = "التقنية", Value = "Visual Studio", Color = Color.FromArgb(46, 134, 171) },
                new { Title = "قاعدة البيانات", Value = "SQLite", Color = Color.FromArgb(243, 146, 55) },
                new { Title = "الحالة", Value = "جاهز للاستخدام", Color = Color.FromArgb(162, 59, 114) }
            };

            for (int i = 0; i < stats.Length; i++)
            {
                Panel statPanel = new Panel
                {
                    Size = new Size(200, 100),
                    Location = new Point(20 + (i * 220), 100),
                    BackColor = stats[i].Color
                };

                Label valueLabel = new Label
                {
                    Text = stats[i].Value,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(10, 25),
                    Size = new Size(180, 40),
                    TextAlign = ContentAlignment.MiddleCenter
                };

                Label titleLabel2 = new Label
                {
                    Text = stats[i].Title,
                    Font = new Font("Segoe UI", 9F),
                    ForeColor = Color.White,
                    Location = new Point(10, 65),
                    Size = new Size(180, 20),
                    TextAlign = ContentAlignment.MiddleCenter
                };

                statPanel.Controls.AddRange(new Control[] { valueLabel, titleLabel2 });
                contentPanel.Controls.Add(statPanel);
            }

            Label successLabel = new Label
            {
                Text = "🎉 تم إنشاء النظام في Visual Studio 2022 بنجاح!",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(58, 125, 68),
                Location = new Point(20, 230),
                Size = new Size(900, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, descLabel, successLabel });
        }

        private void ShowProducts()
        {
            contentPanel.Controls.Clear();

            Label titleLabel = new Label
            {
                Text = "إدارة المنتجات",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            Label infoLabel = new Label
            {
                Text = "هذه الصفحة ستحتوي على جدول المنتجات وأزرار الإدارة",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray,
                Location = new Point(20, 60),
                AutoSize = true
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, infoLabel });
        }

        private void ShowCustomers()
        {
            contentPanel.Controls.Clear();

            Label titleLabel = new Label
            {
                Text = "إدارة العملاء",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            Label infoLabel = new Label
            {
                Text = "هذه الصفحة ستحتوي على جدول العملاء وأزرار الإدارة",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray,
                Location = new Point(20, 60),
                AutoSize = true
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, infoLabel });
        }

        private void AddProduct()
        {
            MessageBox.Show("سيتم فتح نموذج إضافة منتج", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddCustomer()
        {
            MessageBox.Show("سيتم فتح نموذج إضافة عميل", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAbout()
        {
            MessageBox.Show(
                "نظام المحاسبة المتكامل\nVisual Studio 2022 - C# Desktop\n\n" +
                "تم إنشاء النظام بنجاح في Visual Studio 2022\n" +
                "مع الحفاظ على نفس التصميم والألوان الأصلية\n\n" +
                "المميزات:\n" +
                "✅ Visual Studio 2022 Project\n" +
                "✅ Windows Forms Designer\n" +
                "✅ SQLite Database\n" +
                "✅ واجهة عربية كاملة\n" +
                "✅ نفس الألوان والتصميم\n\n" +
                "الإصدار: 1.0\n" +
                "تاريخ الإنشاء: " + DateTime.Now.ToString("yyyy-MM-dd"),
                "حول النظام",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void Logout()
        {
            currentUser = null;
            ShowLoginInterface();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            connection?.Close();
            base.OnFormClosing(e);
        }
    }
    
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
    }
}
