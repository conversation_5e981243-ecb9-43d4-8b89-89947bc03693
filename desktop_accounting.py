#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة - تطبيق سطح المكتب
Desktop Accounting System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
from datetime import datetime, date
import hashlib
import json

class AccountingDesktopApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام المحاسبة المتكامل - Integrated Accounting System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات النظام
        self.current_user = None
        self.user_role = None
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إنشاء الواجهة
        self.create_login_interface()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.conn = sqlite3.connect('accounting_desktop.db')
        self.conn.row_factory = sqlite3.Row
        
        # إنشاء الجداول
        self.create_tables()
        self.insert_sample_data()
        
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                sku TEXT UNIQUE,
                price REAL NOT NULL,
                cost REAL NOT NULL,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                total_amount REAL NOT NULL,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول عناصر الفواتير
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                product_id INTEGER,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        self.conn.commit()
        
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        
        # التحقق من وجود المستخدمين
        users_count = self.conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
        
        if users_count == 0:
            # إضافة مستخدمين افتراضيين
            users = [
                ('admin', self.hash_password('admin123'), 'المدير العام', 'admin', '<EMAIL>'),
                ('accountant', self.hash_password('acc123'), 'المحاسب الرئيسي', 'accountant', '<EMAIL>'),
                ('manager', self.hash_password('mgr123'), 'مدير المبيعات', 'manager', '<EMAIL>')
            ]
            
            self.conn.executemany('''
                INSERT INTO users (username, password, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            ''', users)
            
            # إضافة منتجات تجريبية
            products = [
                ('لابتوب Dell', 'DELL001', 2500.00, 2000.00, 10, 2, 'لابتوب Dell Inspiron 15'),
                ('ماوس لاسلكي', 'MOUSE001', 50.00, 30.00, 25, 5, 'ماوس لاسلكي عالي الجودة'),
                ('كيبورد ميكانيكي', 'KB001', 150.00, 100.00, 15, 3, 'كيبورد ميكانيكي للألعاب'),
                ('شاشة 24 بوصة', 'MON001', 800.00, 600.00, 8, 2, 'شاشة LED 24 بوصة Full HD')
            ]
            
            self.conn.executemany('''
                INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', products)
            
            # إضافة عملاء تجريبيين
            customers = [
                ('شركة التقنية المتقدمة', '<EMAIL>', '0501234567', 'الرياض، المملكة العربية السعودية'),
                ('مؤسسة الأعمال الذكية', '<EMAIL>', '0507654321', 'جدة، المملكة العربية السعودية'),
                ('متجر الإلكترونيات الحديثة', '<EMAIL>', '0551234567', 'الدمام، المملكة العربية السعودية')
            ]
            
            self.conn.executemany('''
                INSERT INTO customers (name, email, phone, address)
                VALUES (?, ?, ?, ?)
            ''', customers)
            
            self.conn.commit()
            
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # إطار تسجيل الدخول
        login_frame = tk.Frame(self.root, bg='white', padx=50, pady=50)
        login_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # عنوان النظام
        title_label = tk.Label(login_frame, text="نظام المحاسبة المتكامل", 
                              font=('Arial', 24, 'bold'), bg='white', fg='#2E86AB')
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(login_frame, text="Integrated Accounting System", 
                                 font=('Arial', 14), bg='white', fg='#666')
        subtitle_label.pack(pady=(0, 30))
        
        # حقول تسجيل الدخول
        tk.Label(login_frame, text="اسم المستخدم:", font=('Arial', 12), bg='white').pack(anchor='w')
        self.username_entry = tk.Entry(login_frame, font=('Arial', 12), width=25)
        self.username_entry.pack(pady=(5, 15))
        
        tk.Label(login_frame, text="كلمة المرور:", font=('Arial', 12), bg='white').pack(anchor='w')
        self.password_entry = tk.Entry(login_frame, font=('Arial', 12), width=25, show='*')
        self.password_entry.pack(pady=(5, 20))
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_frame, text="تسجيل الدخول", font=('Arial', 12, 'bold'),
                             bg='#2E86AB', fg='white', width=20, command=self.login)
        login_btn.pack(pady=10)
        
        # معلومات تسجيل الدخول
        info_frame = tk.Frame(login_frame, bg='white')
        info_frame.pack(pady=20)
        
        tk.Label(info_frame, text="بيانات تسجيل الدخول التجريبية:", 
                font=('Arial', 10, 'bold'), bg='white').pack()
        tk.Label(info_frame, text="المدير: admin / admin123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        tk.Label(info_frame, text="المحاسب: accountant / acc123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        tk.Label(info_frame, text="المدير: manager / mgr123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        # التحقق من بيانات المستخدم
        hashed_password = self.hash_password(password)
        user = self.conn.execute('''
            SELECT * FROM users WHERE username = ? AND password = ?
        ''', (username, hashed_password)).fetchone()
        
        if user:
            self.current_user = dict(user)
            self.user_role = user['role']
            self.create_main_interface()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # شريط القوائم
        self.create_menu_bar()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط المعلومات العلوي
        info_frame = tk.Frame(main_frame, bg='#2E86AB', height=60)
        info_frame.pack(fill='x', pady=(0, 10))
        info_frame.pack_propagate(False)
        
        welcome_label = tk.Label(info_frame, text=f"مرحباً، {self.current_user['full_name']}", 
                                font=('Arial', 14, 'bold'), bg='#2E86AB', fg='white')
        welcome_label.pack(side='left', padx=20, pady=15)
        
        time_label = tk.Label(info_frame, text=datetime.now().strftime("%Y-%m-%d %H:%M"), 
                             font=('Arial', 12), bg='#2E86AB', fg='white')
        time_label.pack(side='right', padx=20, pady=15)
        
        # منطقة المحتوى
        self.content_frame = tk.Frame(main_frame, bg='white')
        self.content_frame.pack(fill='both', expand=True)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة النظام
        system_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="النظام", menu=system_menu)
        system_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        system_menu.add_separator()
        system_menu.add_command(label="تسجيل الخروج", command=self.logout)
        system_menu.add_command(label="إغلاق", command=self.root.quit)
        
        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المنتجات", menu=products_menu)
        products_menu.add_command(label="عرض المنتجات", command=self.show_products)
        products_menu.add_command(label="إضافة منتج", command=self.add_product)
        
        # قائمة العملاء
        customers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="العملاء", menu=customers_menu)
        customers_menu.add_command(label="عرض العملاء", command=self.show_customers)
        customers_menu.add_command(label="إضافة عميل", command=self.add_customer)
        
        # قائمة الفواتير
        invoices_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الفواتير", menu=invoices_menu)
        invoices_menu.add_command(label="عرض الفواتير", command=self.show_invoices)
        invoices_menu.add_command(label="إنشاء فاتورة", command=self.create_invoice)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
            
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content_frame()
        
        # عنوان لوحة التحكم
        title_label = tk.Label(self.content_frame, text="لوحة التحكم", 
                              font=('Arial', 18, 'bold'), bg='white')
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # حساب الإحصائيات
        products_count = self.conn.execute('SELECT COUNT(*) FROM products').fetchone()[0]
        customers_count = self.conn.execute('SELECT COUNT(*) FROM customers').fetchone()[0]
        invoices_count = self.conn.execute('SELECT COUNT(*) FROM invoices').fetchone()[0]
        total_sales = self.conn.execute('SELECT SUM(total_amount) FROM invoices').fetchone()[0] or 0
        
        # عرض الإحصائيات
        stats = [
            ("المنتجات", products_count, "#3A7D44"),
            ("العملاء", customers_count, "#2E86AB"),
            ("الفواتير", invoices_count, "#F39237"),
            ("إجمالي المبيعات", f"{total_sales:.2f} ر.س", "#A23B72")
        ]
        
        for i, (title, value, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, width=200, height=100)
            stat_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            stat_frame.pack_propagate(False)
            
            tk.Label(stat_frame, text=str(value), font=('Arial', 16, 'bold'), 
                    bg=color, fg='white').pack(expand=True)
            tk.Label(stat_frame, text=title, font=('Arial', 10), 
                    bg=color, fg='white').pack()
        
        # تكوين الأعمدة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        stats_frame.grid_columnconfigure(2, weight=1)
        stats_frame.grid_columnconfigure(3, weight=1)
        
    def show_products(self):
        """عرض المنتجات"""
        self.clear_content_frame()

        # عنوان الصفحة
        title_label = tk.Label(self.content_frame, text="إدارة المنتجات",
                              font=('Arial', 18, 'bold'), bg='white')
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.content_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)

        add_btn = tk.Button(buttons_frame, text="إضافة منتج جديد",
                           font=('Arial', 10, 'bold'), bg='#3A7D44', fg='white',
                           command=self.add_product)
        add_btn.pack(side='left', padx=5)

        refresh_btn = tk.Button(buttons_frame, text="تحديث",
                               font=('Arial', 10), bg='#2E86AB', fg='white',
                               command=self.show_products)
        refresh_btn.pack(side='left', padx=5)

        # جدول المنتجات
        tree_frame = tk.Frame(self.content_frame, bg='white')
        tree_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ('ID', 'اسم المنتج', 'الكود', 'السعر', 'التكلفة', 'المخزون', 'الحد الأدنى')
        self.products_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=100, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.products_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # تحميل البيانات
        self.load_products_data()

        # ربط النقر المزدوج
        self.products_tree.bind('<Double-1>', self.edit_product)

    def load_products_data(self):
        """تحميل بيانات المنتجات"""
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # جلب المنتجات من قاعدة البيانات
        products = self.conn.execute('''
            SELECT id, name, sku, price, cost, stock_quantity, min_stock_level
            FROM products ORDER BY name
        ''').fetchall()

        # إضافة البيانات للجدول
        for product in products:
            # تلوين المنتجات منخفضة المخزون
            tags = ()
            if product['stock_quantity'] <= product['min_stock_level']:
                tags = ('low_stock',)

            self.products_tree.insert('', 'end', values=(
                product['id'],
                product['name'],
                product['sku'] or '',
                f"{product['price']:.2f}",
                f"{product['cost']:.2f}",
                product['stock_quantity'],
                product['min_stock_level']
            ), tags=tags)

        # تنسيق الألوان
        self.products_tree.tag_configure('low_stock', background='#ffcccc')

    def add_product(self):
        """إضافة منتج جديد"""
        self.product_dialog()

    def edit_product(self, event):
        """تعديل منتج"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_id = item['values'][0]
            self.product_dialog(product_id)

    def product_dialog(self, product_id=None):
        """نافذة إضافة/تعديل منتج"""
        # إنشاء النافذة
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد" if product_id is None else "تعديل منتج")
        dialog.geometry("400x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # متغيرات النموذج
        name_var = tk.StringVar()
        sku_var = tk.StringVar()
        price_var = tk.StringVar()
        cost_var = tk.StringVar()
        stock_var = tk.StringVar()
        min_stock_var = tk.StringVar()
        description_var = tk.StringVar()

        # تحميل البيانات للتعديل
        if product_id:
            product = self.conn.execute(
                'SELECT * FROM products WHERE id = ?', (product_id,)
            ).fetchone()
            if product:
                name_var.set(product['name'])
                sku_var.set(product['sku'] or '')
                price_var.set(str(product['price']))
                cost_var.set(str(product['cost']))
                stock_var.set(str(product['stock_quantity']))
                min_stock_var.set(str(product['min_stock_level']))
                description_var.set(product['description'] or '')

        # عنوان النافذة
        title_label = tk.Label(dialog, text="بيانات المنتج",
                              font=('Arial', 16, 'bold'), bg='white')
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=10)

        # حقول النموذج
        fields = [
            ("اسم المنتج:", name_var),
            ("كود المنتج:", sku_var),
            ("سعر البيع:", price_var),
            ("سعر التكلفة:", cost_var),
            ("الكمية الحالية:", stock_var),
            ("الحد الأدنى:", min_stock_var),
            ("الوصف:", description_var)
        ]

        entries = {}
        for i, (label_text, var) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Arial', 10),
                    bg='white').grid(row=i, column=0, sticky='w', pady=5)

            if label_text == "الوصف:":
                entry = tk.Text(form_frame, font=('Arial', 10), width=30, height=3)
                entry.insert('1.0', var.get())
                entries[label_text] = entry
            else:
                entry = tk.Entry(form_frame, font=('Arial', 10), width=30, textvariable=var)
                entries[label_text] = entry

            entry.grid(row=i, column=1, sticky='ew', pady=5, padx=(10, 0))

        form_frame.grid_columnconfigure(1, weight=1)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_product():
            try:
                # التحقق من البيانات
                name = name_var.get().strip()
                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                    return

                price = float(price_var.get() or 0)
                cost = float(cost_var.get() or 0)
                stock = int(stock_var.get() or 0)
                min_stock = int(min_stock_var.get() or 0)
                description = entries["الوصف:"].get('1.0', 'end-1c')

                if product_id:
                    # تحديث المنتج
                    self.conn.execute('''
                        UPDATE products SET name=?, sku=?, price=?, cost=?,
                               stock_quantity=?, min_stock_level=?, description=?
                        WHERE id=?
                    ''', (name, sku_var.get(), price, cost, stock, min_stock, description, product_id))
                    messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
                else:
                    # إضافة منتج جديد
                    self.conn.execute('''
                        INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (name, sku_var.get(), price, cost, stock, min_stock, description))
                    messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")

                self.conn.commit()
                dialog.destroy()
                self.show_products()  # تحديث القائمة

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للأرقام")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

        save_btn = tk.Button(buttons_frame, text="حفظ", font=('Arial', 10, 'bold'),
                            bg='#3A7D44', fg='white', command=save_product)
        save_btn.pack(side='right', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=('Arial', 10),
                              bg='#dc3545', fg='white', command=dialog.destroy)
        cancel_btn.pack(side='right', padx=5)

    def show_customers(self):
        """عرض العملاء"""
        self.clear_content_frame()

        # عنوان الصفحة
        title_label = tk.Label(self.content_frame, text="إدارة العملاء",
                              font=('Arial', 18, 'bold'), bg='white')
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.content_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)

        add_btn = tk.Button(buttons_frame, text="إضافة عميل جديد",
                           font=('Arial', 10, 'bold'), bg='#2E86AB', fg='white',
                           command=self.add_customer)
        add_btn.pack(side='left', padx=5)

        # جدول العملاء
        tree_frame = tk.Frame(self.content_frame, bg='white')
        tree_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'اسم العميل', 'البريد الإلكتروني', 'الهاتف', 'العنوان')
        self.customers_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150, anchor='center')

        scrollbar2 = ttk.Scrollbar(tree_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar2.set)

        self.customers_tree.pack(side='left', fill='both', expand=True)
        scrollbar2.pack(side='right', fill='y')

        self.load_customers_data()
        self.customers_tree.bind('<Double-1>', self.edit_customer)

    def load_customers_data(self):
        """تحميل بيانات العملاء"""
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        customers = self.conn.execute('''
            SELECT id, name, email, phone, address FROM customers ORDER BY name
        ''').fetchall()

        for customer in customers:
            self.customers_tree.insert('', 'end', values=(
                customer['id'],
                customer['name'],
                customer['email'] or '',
                customer['phone'] or '',
                customer['address'] or ''
            ))

    def add_customer(self):
        """إضافة عميل جديد"""
        self.customer_dialog()

    def edit_customer(self, event):
        """تعديل عميل"""
        selection = self.customers_tree.selection()
        if selection:
            item = self.customers_tree.item(selection[0])
            customer_id = item['values'][0]
            self.customer_dialog(customer_id)

    def customer_dialog(self, customer_id=None):
        """نافذة إضافة/تعديل عميل"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة عميل جديد" if customer_id is None else "تعديل عميل")
        dialog.geometry("400x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # متغيرات النموذج
        name_var = tk.StringVar()
        email_var = tk.StringVar()
        phone_var = tk.StringVar()
        address_var = tk.StringVar()

        # تحميل البيانات للتعديل
        if customer_id:
            customer = self.conn.execute(
                'SELECT * FROM customers WHERE id = ?', (customer_id,)
            ).fetchone()
            if customer:
                name_var.set(customer['name'])
                email_var.set(customer['email'] or '')
                phone_var.set(customer['phone'] or '')
                address_var.set(customer['address'] or '')

        # عنوان النافذة
        title_label = tk.Label(dialog, text="بيانات العميل",
                              font=('Arial', 16, 'bold'), bg='white')
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(fill='both', expand=True, padx=30, pady=10)

        # حقول النموذج
        fields = [
            ("اسم العميل:", name_var),
            ("البريد الإلكتروني:", email_var),
            ("رقم الهاتف:", phone_var),
            ("العنوان:", address_var)
        ]

        for i, (label_text, var) in enumerate(fields):
            tk.Label(form_frame, text=label_text, font=('Arial', 10),
                    bg='white').grid(row=i, column=0, sticky='w', pady=10)

            if label_text == "العنوان:":
                entry = tk.Text(form_frame, font=('Arial', 10), width=30, height=3)
                entry.insert('1.0', var.get())
            else:
                entry = tk.Entry(form_frame, font=('Arial', 10), width=30, textvariable=var)

            entry.grid(row=i, column=1, sticky='ew', pady=10, padx=(10, 0))

            if label_text == "العنوان:":
                address_entry = entry

        form_frame.grid_columnconfigure(1, weight=1)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_customer():
            try:
                name = name_var.get().strip()
                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                    return

                address = address_entry.get('1.0', 'end-1c')

                if customer_id:
                    self.conn.execute('''
                        UPDATE customers SET name=?, email=?, phone=?, address=?
                        WHERE id=?
                    ''', (name, email_var.get(), phone_var.get(), address, customer_id))
                    messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
                else:
                    self.conn.execute('''
                        INSERT INTO customers (name, email, phone, address)
                        VALUES (?, ?, ?, ?)
                    ''', (name, email_var.get(), phone_var.get(), address))
                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")

                self.conn.commit()
                dialog.destroy()
                self.show_customers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

        save_btn = tk.Button(buttons_frame, text="حفظ", font=('Arial', 10, 'bold'),
                            bg='#2E86AB', fg='white', command=save_customer)
        save_btn.pack(side='right', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=('Arial', 10),
                              bg='#dc3545', fg='white', command=dialog.destroy)
        cancel_btn.pack(side='right', padx=5)

    def show_invoices(self):
        """عرض الفواتير"""
        messagebox.showinfo("قريباً", "صفحة الفواتير قيد التطوير")

    def create_invoice(self):
        """إنشاء فاتورة جديدة"""
        messagebox.showinfo("قريباً", "إنشاء الفواتير قيد التطوير")

    def sales_report(self):
        """تقرير المبيعات"""
        messagebox.showinfo("قريباً", "تقرير المبيعات قيد التطوير")

    def inventory_report(self):
        """تقرير المخزون"""
        messagebox.showinfo("قريباً", "تقرير المخزون قيد التطوير")

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.user_role = None
        self.create_login_interface()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AccountingDesktopApp()
    app.run()
