#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء اختصار على سطح المكتب لنظام المحاسبة
"""

import os
import sys
import winshell
from win32com.client import Dispatch
from pathlib import Path

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    
    try:
        # مسار سطح المكتب
        desktop = winshell.desktop()
        
        # مسار الملف التنفيذي
        exe_path = Path('FinalRelease/AccountingSystem.exe').absolute()
        
        if not exe_path.exists():
            print("❌ لم يتم العثور على الملف التنفيذي")
            print(f"تأكد من وجود الملف في: {exe_path}")
            return False
        
        # إنشاء الاختصار
        shell = Dispatch('WScript.Shell')
        shortcut_path = os.path.join(desktop, 'نظام المحاسبة.lnk')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # إعدادات الاختصار
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.Description = 'نظام المحاسبة المتكامل - Integrated Accounting System'
        shortcut.IconLocation = str(exe_path)  # استخدام أيقونة الملف التنفيذي
        
        # حفظ الاختصار
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب بنجاح!")
        print(f"📍 مسار الاختصار: {shortcut_path}")
        
        return True
        
    except ImportError:
        print("❌ خطأ: مكتبات Windows غير متوفرة")
        print("تثبيت المكتبات المطلوبة:")
        print("pip install pywin32 winshell")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختصار: {e}")
        return False

def create_start_menu_shortcut():
    """إنشاء اختصار في قائمة ابدأ"""
    
    try:
        # مسار قائمة ابدأ
        start_menu = winshell.start_menu()
        programs_folder = os.path.join(start_menu, 'Programs')
        
        # إنشاء مجلد للنظام
        app_folder = os.path.join(programs_folder, 'نظام المحاسبة')
        os.makedirs(app_folder, exist_ok=True)
        
        # مسار الملف التنفيذي
        exe_path = Path('FinalRelease/AccountingSystem.exe').absolute()
        
        # إنشاء الاختصار
        shell = Dispatch('WScript.Shell')
        shortcut_path = os.path.join(app_folder, 'نظام المحاسبة.lnk')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.Description = 'نظام المحاسبة المتكامل'
        shortcut.IconLocation = str(exe_path)
        
        shortcut.save()
        
        print("✅ تم إنشاء اختصار في قائمة ابدأ!")
        print(f"📍 مسار الاختصار: {shortcut_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء اختصار قائمة ابدأ: {e}")
        return False

def install_required_packages():
    """تثبيت المكتبات المطلوبة"""
    
    try:
        import subprocess
        
        packages = ['pywin32', 'winshell']
        
        for package in packages:
            print(f"🔧 تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"❌ فشل تثبيت {package}: {result.stderr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🖥️  إنشاء اختصارات سطح المكتب")
    print("=" * 40)
    
    # التحقق من وجود الملف التنفيذي
    exe_path = Path('FinalRelease/AccountingSystem.exe')
    if not exe_path.exists():
        print("❌ لم يتم العثور على الملف التنفيذي")
        print("تأكد من تشغيل build_final.py أولاً")
        return
    
    # محاولة استيراد المكتبات
    try:
        import winshell
        from win32com.client import Dispatch
    except ImportError:
        print("📦 تثبيت المكتبات المطلوبة...")
        if install_required_packages():
            print("🔄 إعادة تشغيل السكريبت...")
            os.system(f'py "{__file__}"')
            return
        else:
            print("❌ فشل في تثبيت المكتبات")
            return
    
    # إنشاء الاختصارات
    desktop_success = create_desktop_shortcut()
    start_menu_success = create_start_menu_shortcut()
    
    if desktop_success or start_menu_success:
        print("\n🎉 تم إنشاء الاختصارات بنجاح!")
        print("\n📋 كيفية الاستخدام:")
        print("1. انقر مرتين على اختصار سطح المكتب")
        print("2. أو ابحث عن 'نظام المحاسبة' في قائمة ابدأ")
        print("3. سيفتح النظام تلقائياً في المتصفح")
    else:
        print("\n❌ فشل في إنشاء الاختصارات")

if __name__ == "__main__":
    main()
