#!/usr/bin/env python3
"""
قاعدة بيانات مبسطة لنظام المحاسبة
Simple Database for Accounting System
"""

import sqlite3
import json
from datetime import datetime, date
import os

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    
    conn = sqlite3.connect('accounting_system.db')
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'employee',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')
    
    # جدول العملاء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            address TEXT,
            tax_number TEXT,
            customer_type TEXT DEFAULT 'individual',
            credit_limit REAL DEFAULT 0.0,
            current_balance REAL DEFAULT 0.0,
            discount_rate REAL DEFAULT 0.0,
            loyalty_points INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    # جدول فئات المنتجات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            parent_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories (id)
        )
    ''')
    
    # جدول الموردين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            tax_number TEXT,
            payment_terms TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    # جدول المنتجات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            sku TEXT UNIQUE,
            barcode TEXT UNIQUE,
            category_id INTEGER,
            supplier_id INTEGER,
            cost_price REAL NOT NULL DEFAULT 0.0,
            selling_price REAL NOT NULL DEFAULT 0.0,
            wholesale_price REAL DEFAULT 0.0,
            current_stock INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            max_stock_level INTEGER DEFAULT 0,
            unit TEXT DEFAULT 'قطعة',
            weight REAL,
            expiry_date DATE,
            is_active BOOLEAN DEFAULT 1,
            is_taxable BOOLEAN DEFAULT 1,
            tax_rate REAL DEFAULT 15.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
    ''')
    
    # جدول الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            customer_id INTEGER,
            created_by INTEGER NOT NULL,
            issue_date DATE DEFAULT CURRENT_DATE,
            due_date DATE,
            subtotal REAL DEFAULT 0.0,
            discount_amount REAL DEFAULT 0.0,
            tax_amount REAL DEFAULT 0.0,
            total_amount REAL DEFAULT 0.0,
            paid_amount REAL DEFAULT 0.0,
            status TEXT DEFAULT 'draft',
            payment_method TEXT,
            notes TEXT,
            terms_conditions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # جدول بنود الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            discount_rate REAL DEFAULT 0.0,
            tax_rate REAL DEFAULT 15.0,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    # جدول المصروفات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reference_number TEXT UNIQUE NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            amount REAL NOT NULL,
            category TEXT NOT NULL,
            vendor TEXT,
            expense_date DATE DEFAULT CURRENT_DATE,
            status TEXT DEFAULT 'pending',
            payment_method TEXT,
            receipt_path TEXT,
            created_by INTEGER NOT NULL,
            approved_by INTEGER,
            approved_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (created_by) REFERENCES users (id),
            FOREIGN KEY (approved_by) REFERENCES users (id)
        )
    ''')
    
    # جدول الموظفين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            address TEXT,
            national_id TEXT,
            position TEXT NOT NULL,
            department TEXT,
            hire_date DATE NOT NULL,
            employment_type TEXT DEFAULT 'full_time',
            basic_salary REAL NOT NULL,
            housing_allowance REAL DEFAULT 0.0,
            transport_allowance REAL DEFAULT 0.0,
            other_allowances REAL DEFAULT 0.0,
            social_insurance_rate REAL DEFAULT 10.0,
            income_tax_rate REAL DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            termination_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء جداول قاعدة البيانات")

def insert_sample_data():
    """إدراج البيانات التجريبية"""
    
    conn = sqlite3.connect('accounting_system.db')
    cursor = conn.cursor()
    
    # إدراج المستخدمين
    users_data = [
        ('admin', '<EMAIL>', 'admin123', 'أحمد', 'المدير', 'admin'),
        ('accountant', '<EMAIL>', 'acc123', 'فاطمة', 'المحاسبة', 'accountant'),
        ('employee', '<EMAIL>', 'emp123', 'محمد', 'الموظف', 'employee'),
        ('manager', '<EMAIL>', 'mgr123', 'سارة', 'المديرة', 'manager')
    ]
    
    for username, email, password, first_name, last_name, role in users_data:
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, password_hash, first_name, last_name, role)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, email, password, first_name, last_name, role))
    
    # إدراج العملاء
    customers_data = [
        ('شركة التقنية المتقدمة', '<EMAIL>', '**********', 'الرياض، المملكة العربية السعودية', '***************', 'company', 50000.0, 5.0),
        ('مؤسسة الحلول الذكية', '<EMAIL>', '**********', 'جدة، المملكة العربية السعودية', '***************', 'company', 30000.0, 3.0),
        ('أحمد محمد العلي', '<EMAIL>', '**********', 'الدمام، المملكة العربية السعودية', '', 'individual', 5000.0, 0.0),
        ('شركة الابتكار التقني', '<EMAIL>', '0114567890', 'الخبر، المملكة العربية السعودية', '300345678901003', 'company', 75000.0, 7.0)
    ]
    
    for name, email, phone, address, tax_number, customer_type, credit_limit, discount_rate in customers_data:
        cursor.execute('''
            INSERT OR IGNORE INTO customers (name, email, phone, address, tax_number, customer_type, credit_limit, discount_rate)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, email, phone, address, tax_number, customer_type, credit_limit, discount_rate))
    
    # إدراج فئات المنتجات
    categories_data = [
        ('الأطعمة والمشروبات', 'جميع المواد الغذائية والمشروبات'),
        ('منتجات الألبان', 'الحليب والجبن والزبدة'),
        ('اللحوم والدواجن', 'اللحوم الطازجة والمجمدة'),
        ('الخضروات والفواكه', 'الخضروات والفواكه الطازجة'),
        ('المخبوزات', 'الخبز والمعجنات'),
        ('المنظفات', 'منتجات التنظيف والعناية'),
        ('العناية الشخصية', 'منتجات العناية والتجميل'),
        ('الأدوات المنزلية', 'الأدوات والمستلزمات المنزلية')
    ]
    
    for name, description in categories_data:
        cursor.execute('''
            INSERT OR IGNORE INTO categories (name, description)
            VALUES (?, ?)
        ''', (name, description))
    
    # إدراج الموردين
    suppliers_data = [
        ('شركة المواد الغذائية المتحدة', 'خالد أحمد', '<EMAIL>', '0112223333', 'الرياض، المملكة العربية السعودية', '300111222333003', 'دفع خلال 30 يوم'),
        ('مؤسسة الألبان الطازجة', 'فاطمة محمد', '<EMAIL>', '0113334444', 'جدة، المملكة العربية السعودية', '300222333444003', 'دفع فوري'),
        ('شركة المنظفات الحديثة', 'عبدالله سالم', '<EMAIL>', '0114445555', 'الدمام، المملكة العربية السعودية', '300333444555003', 'دفع خلال 15 يوم')
    ]
    
    for name, contact_person, email, phone, address, tax_number, payment_terms in suppliers_data:
        cursor.execute('''
            INSERT OR IGNORE INTO suppliers (name, contact_person, email, phone, address, tax_number, payment_terms)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (name, contact_person, email, phone, address, tax_number, payment_terms))
    
    # إدراج المنتجات
    products_data = [
        ('حليب طازج - 1 لتر', 'حليب طازج كامل الدسم', 'MILK001', '6281234567890', 2, 2, 3.50, 5.00, 150, 20, 300, 'لتر'),
        ('خبز أبيض طازج', 'خبز أبيض طازج يومياً', 'BREAD001', '6281234567891', 5, 1, 1.00, 1.50, 80, 10, 200, 'رغيف'),
        ('دجاج طازج - كيلو', 'دجاج طازج محلي', 'CHICKEN001', '6281234567892', 3, 1, 18.00, 25.00, 45, 5, 100, 'كيلو'),
        ('تفاح أحمر - كيلو', 'تفاح أحمر طازج مستورد', 'APPLE001', '6281234567893', 4, 1, 8.00, 12.00, 25, 10, 80, 'كيلو'),
        ('منظف أطباق - 500 مل', 'منظف أطباق فعال ضد الدهون', 'DETERGENT001', '6281234567894', 6, 3, 4.50, 7.00, 60, 15, 120, 'زجاجة')
    ]
    
    for name, description, sku, barcode, category_id, supplier_id, cost_price, selling_price, current_stock, min_stock, max_stock, unit in products_data:
        cursor.execute('''
            INSERT OR IGNORE INTO products (name, description, sku, barcode, category_id, supplier_id, cost_price, selling_price, current_stock, min_stock_level, max_stock_level, unit)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, description, sku, barcode, category_id, supplier_id, cost_price, selling_price, current_stock, min_stock, max_stock, unit))
    
    # إدراج الموظفين
    employees_data = [
        ('EMP001', 'أحمد', 'محمد علي', '<EMAIL>', '0501111111', 'مطور أول', 'تقنية المعلومات', '2023-01-15', 8000.0, 1000.0, 500.0),
        ('EMP002', 'فاطمة', 'أحمد السالم', '<EMAIL>', '0502222222', 'محاسبة رئيسية', 'المالية', '2022-06-01', 7000.0, 800.0, 400.0),
        ('EMP003', 'محمد', 'عبدالله الزهراني', '<EMAIL>', '0503333333', 'مدير المبيعات', 'المبيعات', '2021-03-10', 9000.0, 1200.0, 800.0)
    ]
    
    for employee_id, first_name, last_name, email, phone, position, department, hire_date, basic_salary, housing_allowance, transport_allowance in employees_data:
        cursor.execute('''
            INSERT OR IGNORE INTO employees (employee_id, first_name, last_name, email, phone, position, department, hire_date, basic_salary, housing_allowance, transport_allowance)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (employee_id, first_name, last_name, email, phone, position, department, hire_date, basic_salary, housing_allowance, transport_allowance))
    
    conn.commit()
    conn.close()
    print("✅ تم إدراج البيانات التجريبية")

def main():
    """الوظيفة الرئيسية"""
    
    print("🚀 بدء إعداد قاعدة البيانات المبسطة...")
    
    # إنشاء قاعدة البيانات
    create_database()
    
    # إدراج البيانات التجريبية
    insert_sample_data()
    
    print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("📁 ملف قاعدة البيانات: accounting_system.db")
    print("\n🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   الموظف: employee / emp123")
    print("   المدير: manager / mgr123")

if __name__ == '__main__':
    main()
