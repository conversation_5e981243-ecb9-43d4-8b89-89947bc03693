# Integrated Accounting System

## 🚀 How to Run

### Method 1 (Easiest):
1. Double-click on "StartSystem.bat"
2. Wait until you see "Running on http://127.0.0.1:5000"
3. Open your browser and go to: http://localhost:5000

### Method 2:
1. Double-click on "AccountingSystem.exe"
2. Open your browser and go to: http://localhost:5000

## 🔑 Login Credentials

### Admin:
- Username: admin
- Password: admin123

### Accountant:
- Username: accountant
- Password: acc123

### Store Manager:
- Username: manager
- Password: mgr123

## 📁 Important Files

- AccountingSystem.exe - Main executable file
- accounting_system.db - Database file
- StartSystem.bat - Easy startup script
- README.txt - This file

## ⚠️ Important Warnings

1. Do not delete the database file (accounting_system.db)
2. Make regular backups of the database
3. Make sure to close the system properly before shutting down the computer
4. Do not run more than one instance of the system at the same time

## 🔧 Troubleshooting

### Problem: System doesn't open in browser
Solution: Make sure port 5000 is not used by another program

### Problem: Error message when starting
Solution: Run the system as administrator (Run as Administrator)

### Problem: Data loss
Solution: Restore from database backup

---
Developed by Professional Development Team
