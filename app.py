"""
نظام محاسبي متكامل - التطبيق الرئيسي
Integrated Accounting System - Main Application
"""

from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, login_required, current_user
from flask_bcrypt import Bcrypt
from flask_wtf.csrf import CSRFProtect
from datetime import datetime
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///accounting.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة الإضافات
db.init_app(app)
migrate = Migrate(app, db)
bcrypt = Bcrypt(app)
csrf = CSRFProtect(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# استيراد النماذج
from app.models import db, User, Company, Account, Invoice, Expense, Employee, Payroll

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# استيراد المسارات
from app.routes import auth, dashboard, invoices, expenses, payroll, reports, settings

# تسجيل المسارات
app.register_blueprint(auth.bp)
app.register_blueprint(dashboard.bp)
app.register_blueprint(invoices.bp)
app.register_blueprint(expenses.bp)
app.register_blueprint(payroll.bp)
app.register_blueprint(reports.bp)
app.register_blueprint(settings.bp)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    return render_template('index.html')

@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

@app.context_processor
def inject_now():
    """حقن التاريخ والوقت الحالي في جميع القوالب"""
    return {'now': datetime.utcnow()}

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
