<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المحاسبة المتكامل - نسخة تجريبية{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            min-height: 100vh;
        }

        .navbar {
            backdrop-filter: blur(10px);
            background: rgba(4, 120, 87, 0.98) !important;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #047857, #065f46);
            box-shadow: 0 2px 4px rgba(4, 120, 87, 0.3);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .stats-icon.primary {
            background: linear-gradient(135deg, #047857, #065f46);
            box-shadow: 0 4px 12px rgba(4, 120, 87, 0.4);
        }
        .stats-icon.success {
            background: linear-gradient(135deg, #16a34a, #15803d);
            box-shadow: 0 4px 12px rgba(22, 163, 74, 0.4);
        }
        .stats-icon.warning {
            background: linear-gradient(135deg, #ca8a04, #a16207);
            box-shadow: 0 4px 12px rgba(202, 138, 4, 0.4);
        }
        .stats-icon.danger {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #047857, #065f46);
            border: none;
            color: white;
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(4, 120, 87, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #065f46, #064e3b);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(4, 120, 87, 0.5);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #16a34a, #15803d);
            border: none;
            color: white;
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(22, 163, 74, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #15803d, #166534);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 163, 74, 0.5);
            color: white;
        }

        .btn-outline-success {
            border: 2px solid #16a34a;
            color: #16a34a;
            font-weight: 600;
        }

        .btn-outline-success:hover {
            background: #16a34a;
            border-color: #16a34a;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(22, 163, 74, 0.3);
        }
        
        .table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border: none;
            font-weight: 700;
            color: #0f172a;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
        }
        
        .demo-banner {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            text-align: center;
            padding: 0.5rem;
            font-weight: 600;
        }

        .feature-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #22c55e;
        }

        /* Professional Header Styles */
        .professional-header {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(5, 150, 105, 0.95));
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 0 0 30px 30px;
        }

        .company-name {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
        }

        .company-details {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .quick-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }

        .stat-item {
            text-align: center;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .action-buttons .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 1rem;
        }

        /* Module Cards */
        .main-modules {
            padding: 3rem 0;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
        }

        .module-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: 2px solid transparent;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #10b981;
        }

        .module-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .module-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
        }

        .module-icon.invoices { background: linear-gradient(135deg, #10b981, #059669); }
        .module-icon.expenses { background: linear-gradient(135deg, #22c55e, #16a34a); }
        .module-icon.reports { background: linear-gradient(135deg, #84cc16, #65a30d); }
        .module-icon.payroll { background: linear-gradient(135deg, #059669, #047857); }
        .module-icon.accounts { background: linear-gradient(135deg, #0d9488, #0f766e); }
        .module-icon.settings { background: linear-gradient(135deg, #64748b, #475569); }

        .module-header h4 {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0;
        }

        .module-body {
            flex-grow: 1;
            margin-bottom: 2rem;
        }

        .module-body p {
            color: #64748b;
            margin-bottom: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            color: #475569;
            position: relative;
            padding-right: 1.5rem;
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #10b981;
            font-weight: bold;
        }

        .module-footer .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 0.75rem;
        }

        /* Activity Section */
        .recent-activity {
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem 0;
            margin-top: 3rem;
        }

        .activity-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            color: white;
        }

        .activity-icon.success { background: #10b981; }
        .activity-icon.warning { background: #f59e0b; }
        .activity-icon.info { background: #3b82f6; }

        .activity-content h5 {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .activity-content p {
            color: #64748b;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Demo Banner -->
    <div class="demo-banner">
        <i class="fas fa-flask me-2"></i>
        هذه نسخة تجريبية من نظام المحاسبة المتكامل - جميع البيانات وهمية لأغراض العرض
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('invoices') }}">
                            <i class="fas fa-file-invoice me-1"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('expenses') }}">
                            <i class="fas fa-receipt me-1"></i>المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-user-circle me-1"></i>مستخدم تجريبي
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام المحاسبة المتكامل - نسخة تجريبية
                <span class="text-warning ms-3">
                    <i class="fas fa-code me-1"></i>
                    تم التطوير بواجهات أنيقة وعصرية
                </span>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
