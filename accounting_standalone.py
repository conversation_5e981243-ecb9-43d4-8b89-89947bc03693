#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مستقلة من نظام المحاسبة للتشغيل كملف تنفيذي
"""

import sys
import os
import webbrowser
import threading
import time
from pathlib import Path

# إضافة المسار الحالي لـ Python path
if hasattr(sys, '_MEIPASS'):
    # عند التشغيل كملف تنفيذي
    base_path = sys._MEIPASS
else:
    # عند التشغيل كملف Python عادي
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

def setup_database():
    """إعداد قاعدة البيانات إذا لم تكن موجودة"""
    db_path = os.path.join(base_path, 'accounting_system.db')
    if not os.path.exists(db_path):
        print("إنشاء قاعدة بيانات جديدة...")
        # هنا يمكن إضافة كود إنشاء قاعدة البيانات
        return True
    return True

def open_browser():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)  # انتظار 3 ثوان لبدء الخادم
    try:
        webbrowser.open('http://localhost:5000')
        print("تم فتح المتصفح تلقائياً")
    except:
        print("لم يتم فتح المتصفح تلقائياً")
        print("يرجى فتح المتصفح والذهاب إلى: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("           نظام المحاسبة المتكامل")
    print("         Integrated Accounting System")
    print("=" * 60)
    print()
    
    try:
        # إعداد قاعدة البيانات
        if not setup_database():
            print("❌ خطأ في إعداد قاعدة البيانات")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ تم إعداد قاعدة البيانات")
        print("🚀 جاري تشغيل النظام...")
        print()
        
        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # استيراد وتشغيل النظام
        try:
            from accounting_system_db import app
            print("🌐 النظام يعمل على: http://localhost:5000")
            print("📱 بيانات تسجيل الدخول:")
            print("   المدير: admin / admin123")
            print("   المحاسب: accountant / acc123")
            print("   المدير: manager / mgr123")
            print()
            print("⚠️  لإيقاف النظام: اضغط Ctrl+C")
            print("=" * 60)
            
            # تشغيل الخادم
            app.run(
                host='127.0.0.1',
                port=5000,
                debug=False,
                use_reloader=False,
                threaded=True
            )
            
        except ImportError as e:
            print(f"❌ خطأ في استيراد النظام: {e}")
            print("تأكد من وجود جميع الملفات المطلوبة")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        
    finally:
        print("\n👋 شكراً لاستخدام نظام المحاسبة")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
