#!/usr/bin/env python3
"""
تطبيق سطح المكتب لنظام المحاسبة
Desktop Application for Accounting System
"""

import os
import sys
import threading
import time
import webbrowser
import subprocess
from accounting_system import app

def create_desktop_app():
    """إنشاء تطبيق سطح مكتب"""
    
    print("🖥️ تحويل نظام المحاسبة إلى تطبيق سطح مكتب...")
    print("=" * 60)
    
    # إنشاء ملف package.json لـ Electron
    package_json = {
        "name": "accounting-system-desktop",
        "version": "1.0.0",
        "description": "نظام المحاسبة المتكامل - تطبيق سطح المكتب",
        "main": "main.js",
        "scripts": {
            "start": "electron .",
            "build": "electron-builder",
            "dist": "electron-builder --publish=never"
        },
        "author": "Accounting System Team",
        "license": "MIT",
        "devDependencies": {
            "electron": "^27.0.0",
            "electron-builder": "^24.6.4"
        },
        "build": {
            "appId": "com.accounting.system",
            "productName": "نظام المحاسبة المتكامل",
            "directories": {
                "output": "dist"
            },
            "files": [
                "main.js",
                "preload.js",
                "icon.png",
                "accounting_system.py",
                "requirements.txt"
            ],
            "win": {
                "target": "nsis",
                "icon": "icon.png"
            },
            "mac": {
                "target": "dmg",
                "icon": "icon.png"
            },
            "linux": {
                "target": "AppImage",
                "icon": "icon.png"
            }
        }
    }
    
    # كتابة ملف package.json
    import json
    with open('package.json', 'w', encoding='utf-8') as f:
        json.dump(package_json, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء package.json")
    
    # إنشاء ملف main.js (الملف الرئيسي لـ Electron)
    main_js = '''
const { app, BrowserWindow, Menu, shell, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let flaskProcess;

// تشغيل خادم Flask
function startFlaskServer() {
    return new Promise((resolve, reject) => {
        flaskProcess = spawn('python', ['accounting_system.py'], {
            cwd: __dirname,
            stdio: 'pipe'
        });
        
        flaskProcess.stdout.on('data', (data) => {
            console.log(`Flask: ${data}`);
            if (data.toString().includes('Running on')) {
                resolve();
            }
        });
        
        flaskProcess.stderr.on('data', (data) => {
            console.error(`Flask Error: ${data}`);
        });
        
        flaskProcess.on('close', (code) => {
            console.log(`Flask process exited with code ${code}`);
        });
        
        // انتظار 3 ثوان للتأكد من تشغيل الخادم
        setTimeout(resolve, 3000);
    });
}

// إنشاء النافذة الرئيسية
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        titleBarStyle: 'default',
        show: false
    });

    // تحميل التطبيق
    mainWindow.loadURL('http://localhost:5000');
    
    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.focus();
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (flaskProcess) {
            flaskProcess.kill();
        }
    });

    // فتح الروابط الخارجية في المتصفح
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// إنشاء القائمة
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'تحديث',
                    accelerator: 'F5',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        if (mainWindow) {
                            const currentZoom = mainWindow.webContents.getZoomLevel();
                            mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
                        }
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        if (mainWindow) {
                            const currentZoom = mainWindow.webContents.getZoomLevel();
                            mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
                        }
                    }
                },
                {
                    label: 'حجم طبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.setZoomLevel(0);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'ملء الشاشة',
                    accelerator: 'F11',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.setFullScreen(!mainWindow.isFullScreen());
                        }
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'نظام المحاسبة المتكامل',
                            detail: 'الإصدار 1.0.0\\nتطوير: فريق نظام المحاسبة\\n\\nنظام شامل لإدارة العمليات المالية'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// عند جاهزية التطبيق
app.whenReady().then(async () => {
    try {
        console.log('بدء تشغيل خادم Flask...');
        await startFlaskServer();
        console.log('تم تشغيل خادم Flask بنجاح');
        
        createWindow();
        createMenu();
        
        console.log('تم تشغيل تطبيق سطح المكتب بنجاح');
    } catch (error) {
        console.error('خطأ في تشغيل التطبيق:', error);
        dialog.showErrorBox('خطأ', 'فشل في تشغيل النظام. تأكد من تثبيت Python و Flask.');
        app.quit();
    }
});

// إنهاء التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    if (flaskProcess) {
        flaskProcess.kill();
    }
    app.quit();
});

// إعادة تفعيل التطبيق (macOS)
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// التعامل مع إنهاء التطبيق
app.on('before-quit', () => {
    if (flaskProcess) {
        flaskProcess.kill();
    }
});
'''
    
    with open('main.js', 'w', encoding='utf-8') as f:
        f.write(main_js)
    
    print("✅ تم إنشاء main.js")
    
    # إنشاء ملف preload.js
    preload_js = '''
const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // يمكن إضافة وظائف مخصصة هنا
    platform: process.platform,
    version: process.versions.electron
});
'''
    
    with open('preload.js', 'w', encoding='utf-8') as f:
        f.write(preload_js)
    
    print("✅ تم إنشاء preload.js")
    
    return True

def install_electron():
    """تثبيت Electron و Node.js"""
    
    print("📦 تثبيت متطلبات تطبيق سطح المكتب...")
    
    try:
        # التحقق من وجود Node.js
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js متوفر: {result.stdout.strip()}")
        else:
            print("❌ Node.js غير مثبت")
            print("💡 قم بتحميل Node.js من: https://nodejs.org/")
            return False
            
    except FileNotFoundError:
        print("❌ Node.js غير مثبت")
        print("💡 قم بتحميل Node.js من: https://nodejs.org/")
        return False
    
    try:
        # تثبيت Electron
        print("📦 تثبيت Electron...")
        result = subprocess.run(['npm', 'install'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت Electron بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت Electron: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ npm غير متوفر")
        return False

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    
    print("🚀 تشغيل تطبيق سطح المكتب...")
    
    try:
        # تشغيل التطبيق
        subprocess.run(['npm', 'start'], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل التطبيق: {e}")
        return False
    except FileNotFoundError:
        print("❌ npm غير متوفر")
        return False
    
    return True

def build_executable():
    """بناء ملف تنفيذي للتوزيع"""
    
    print("🔨 بناء ملف تنفيذي...")
    
    try:
        # بناء التطبيق
        result = subprocess.run(['npm', 'run', 'dist'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح")
            print("📁 الملفات متوفرة في مجلد: dist/")
            return True
        else:
            print(f"❌ فشل في البناء: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ npm غير متوفر")
        return False

def show_instructions():
    """عرض تعليمات الاستخدام"""
    
    print("📚 تعليمات تحويل النظام إلى تطبيق سطح مكتب")
    print("=" * 60)
    print()
    print("🔧 الخطوات المطلوبة:")
    print()
    print("1️⃣  تثبيت Node.js:")
    print("   - قم بتحميل Node.js من: https://nodejs.org/")
    print("   - اختر النسخة LTS (الموصى بها)")
    print("   - قم بتثبيتها واتبع التعليمات")
    print()
    print("2️⃣  إنشاء تطبيق سطح المكتب:")
    print("   python desktop_app.py --create")
    print()
    print("3️⃣  تثبيت المتطلبات:")
    print("   python desktop_app.py --install")
    print()
    print("4️⃣  تشغيل التطبيق:")
    print("   python desktop_app.py --run")
    print()
    print("5️⃣  بناء ملف تنفيذي (اختياري):")
    print("   python desktop_app.py --build")
    print()
    print("💡 ملاحظات:")
    print("   • يحتاج Node.js لتشغيل Electron")
    print("   • التطبيق سيعمل كنافذة منفصلة")
    print("   • يمكن توزيع الملف التنفيذي بدون Python")
    print()

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            show_instructions()
        elif sys.argv[1] == '--create':
            if create_desktop_app():
                print("\n🎉 تم إنشاء ملفات تطبيق سطح المكتب بنجاح!")
                print("📋 الخطوة التالية: python desktop_app.py --install")
        elif sys.argv[1] == '--install':
            if install_electron():
                print("\n🎉 تم تثبيت المتطلبات بنجاح!")
                print("📋 الخطوة التالية: python desktop_app.py --run")
        elif sys.argv[1] == '--run':
            run_desktop_app()
        elif sys.argv[1] == '--build':
            build_executable()
        else:
            print("❌ خيار غير معروف. استخدم --help للمساعدة")
    else:
        show_instructions()
