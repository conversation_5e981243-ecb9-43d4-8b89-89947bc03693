#!/usr/bin/env python3
"""
مشغل نظام المحاسبة المتكامل
Accounting System Launcher
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_database():
    """التحقق من وجود قاعدة البيانات"""
    if not os.path.exists('accounting_system.db'):
        print("❌ قاعدة البيانات غير موجودة!")
        print("🔧 جاري إنشاء قاعدة البيانات...")
        
        try:
            subprocess.run([sys.executable, 'simple_database.py'], check=True)
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء قاعدة البيانات!")
            return False
        except FileNotFoundError:
            print("❌ ملف simple_database.py غير موجود!")
            return False
    
    return True

def run_system():
    """تشغيل النظام"""
    
    print("🚀 مشغل نظام المحاسبة المتكامل")
    print("=" * 50)
    
    # التحقق من قاعدة البيانات
    if not check_database():
        input("اضغط Enter للخروج...")
        return
    
    print("\n📊 معلومات النظام:")
    print("   📁 قاعدة البيانات: accounting_system.db")
    print("   🌐 العنوان: http://localhost:5000")
    print("   🔑 بيانات تسجيل الدخول:")
    print("      • المدير: admin / admin123")
    print("      • المحاسب: accountant / acc123")
    print("      • المدير: manager / mgr123")
    
    print("\n🔄 جاري تشغيل النظام...")
    
    try:
        # تشغيل النظام
        process = subprocess.Popen([sys.executable, 'accounting_system_db.py'])
        
        # انتظار قليل ثم فتح المتصفح
        print("⏳ انتظار تشغيل الخادم...")
        time.sleep(3)
        
        print("🌐 فتح المتصفح...")
        webbrowser.open('http://localhost:5000')
        
        print("\n✅ تم تشغيل النظام بنجاح!")
        print("📱 النظام متاح على: http://localhost:5000")
        print("\n💡 لإيقاف النظام: اضغط Ctrl+C")
        print("=" * 50)
        
        # انتظار إيقاف النظام
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 جاري إيقاف النظام...")
            process.terminate()
            process.wait()
            print("✅ تم إيقاف النظام بنجاح!")
    
    except FileNotFoundError:
        print("❌ ملف accounting_system_db.py غير موجود!")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    run_system()
