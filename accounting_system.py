import os
os.environ['FLASK_ENV'] = 'production'#!/usr/bin/env python3
"""
نظام المحاسبة المتكامل - النسخة النهائية
Integrated Accounting System - Final Version
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
import json
from datetime import datetime, date, timedelta

app = Flask(__name__)
app.secret_key = 'accounting-system-2024-secret-key'

# بيانات المستخدمين
USERS = {
    'admin': {
        'password': 'admin123', 
        'name': 'أحمد المدير', 
        'role': 'مدير',
        'permissions': ['all']
    },
    'accountant': {
        'password': 'acc123', 
        'name': 'فاطمة المحاسبة', 
        'role': 'محاسب',
        'permissions': ['invoices', 'expenses', 'reports']
    },
    'employee': {
        'password': 'emp123', 
        'name': 'محمد الموظف', 
        'role': 'موظف',
        'permissions': ['expenses']
    }
}

# بيانات تجريبية للفواتير
SAMPLE_INVOICES = [
    {
        'id': 1,
        'invoice_number': 'INV-2024-0001',
        'customer_name': 'شركة التقنية المتقدمة',
        'total_amount': 25000,
        'status': 'paid',
        'status_text': 'مدفوعة',
        'status_color': 'success',
        'issue_date': '2024-12-01',
        'due_date': '2024-12-31',
        'items': [
            {'description': 'تطوير نظام إدارة المحتوى', 'quantity': 1, 'unit_price': 20000, 'total': 20000},
            {'description': 'استشارات تقنية', 'quantity': 10, 'unit_price': 500, 'total': 5000}
        ]
    },
    {
        'id': 2,
        'invoice_number': 'INV-2024-0002',
        'customer_name': 'مؤسسة الحلول الذكية',
        'total_amount': 18500,
        'status': 'sent',
        'status_text': 'مرسلة',
        'status_color': 'info',
        'issue_date': '2024-12-15',
        'due_date': '2025-01-15',
        'items': [
            {'description': 'تصميم موقع إلكتروني', 'quantity': 1, 'unit_price': 15000, 'total': 15000},
            {'description': 'صيانة شهرية', 'quantity': 1, 'unit_price': 3500, 'total': 3500}
        ]
    },
    {
        'id': 3,
        'invoice_number': 'INV-2024-0003',
        'customer_name': 'شركة الابتكار التقني',
        'total_amount': 32000,
        'status': 'overdue',
        'status_text': 'متأخرة',
        'status_color': 'danger',
        'issue_date': '2024-11-20',
        'due_date': '2024-12-20',
        'items': [
            {'description': 'تطوير تطبيق جوال', 'quantity': 1, 'unit_price': 30000, 'total': 30000},
            {'description': 'تدريب المستخدمين', 'quantity': 4, 'unit_price': 500, 'total': 2000}
        ]
    }
]

# بيانات تجريبية للمصروفات
SAMPLE_EXPENSES = [
    {
        'id': 1,
        'reference_number': 'EXP-2024-0015',
        'title': 'إيجار المكتب - شهر ديسمبر',
        'amount': 12000,
        'category': 'الإيجار',
        'status': 'paid',
        'status_text': 'مدفوع',
        'status_color': 'success',
        'expense_date': '2024-12-01',
        'vendor': 'شركة العقارات المتقدمة'
    },
    {
        'id': 2,
        'reference_number': 'EXP-2024-0016',
        'title': 'فاتورة الكهرباء',
        'amount': 850,
        'category': 'المرافق',
        'status': 'approved',
        'status_text': 'موافق عليه',
        'status_color': 'info',
        'expense_date': '2024-12-28',
        'vendor': 'الشركة السعودية للكهرباء'
    },
    {
        'id': 3,
        'reference_number': 'EXP-2024-0017',
        'title': 'مستلزمات مكتبية',
        'amount': 450,
        'category': 'المكتبية',
        'status': 'pending',
        'status_text': 'في انتظار الموافقة',
        'status_color': 'warning',
        'expense_date': '2024-12-28',
        'vendor': 'مكتبة الرياض'
    },
    {
        'id': 4,
        'reference_number': 'EXP-2024-0018',
        'title': 'حملة إعلانية على جوجل',
        'amount': 2500,
        'category': 'التسويق',
        'status': 'paid',
        'status_text': 'مدفوع',
        'status_color': 'success',
        'expense_date': '2024-12-25',
        'vendor': 'Google Ads'
    }
]

# قالب القاعدة الأساسية
BASE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المحاسبة المتكامل{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
        }
        .navbar { background: rgba(5, 150, 105, 0.95) !important; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        .btn-primary { background: linear-gradient(135deg, #10b981, #059669); border: none; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4); }
        .alert { border: none; border-radius: 12px; }
        .alert-success { background: rgba(16, 185, 129, 0.1); color: #059669; border-right: 4px solid #10b981; }
        .alert-danger { background: rgba(239, 68, 68, 0.1); color: #dc2626; border-right: 4px solid #ef4444; }
        .alert-info { background: rgba(59, 130, 246, 0.1); color: #2563eb; border-right: 4px solid #3b82f6; }
        .table { background: white; border-radius: 15px; overflow: hidden; }
        .table thead th { background: linear-gradient(135deg, #f8fafc, #e2e8f0); border: none; font-weight: 600; color: #1e293b; }
        .badge { font-size: 0.75rem; padding: 0.5rem 1rem; border-radius: 50px; }
    </style>
</head>
<body>
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/invoices">
                            <i class="fas fa-file-invoice me-1"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/expenses">
                            <i class="fas fa-receipt me-1"></i>المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/payroll">
                            <i class="fas fa-users me-1"></i>الرواتب
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ session.user_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <main class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
'''

# قالب صفحة تسجيل الدخول
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .login-logo i {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
        .demo-info {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .demo-credentials {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .credential-item:last-child { border-bottom: none; }
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border-right: 4px solid #ef4444;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border-right: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-logo text-center">
            <i class="fas fa-calculator"></i>
            <h2 class="fw-bold" style="color: #10b981;">نظام المحاسبة المتكامل</h2>
            <p class="text-muted">مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="demo-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>نسخة تجريبية</strong> - استخدم البيانات أدناه لتسجيل الدخول
        </div>

        <form method="POST" id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label fw-bold">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>

        <div class="demo-credentials">
            <h6 style="color: #059669;"><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول التجريبية:</h6>
            
            <div class="credential-item">
                <span><strong>المدير:</strong></span>
                <div>
                    <code>admin</code> / <code>admin123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('admin', 'admin123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>المحاسب:</strong></span>
                <div>
                    <code>accountant</code> / <code>acc123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('accountant', 'acc123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>الموظف:</strong></span>
                <div>
                    <code>employee</code> / <code>emp123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('employee', 'emp123')">
                        استخدام
                    </button>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <small class="text-muted">
                &copy; 2024 نظام المحاسبة المتكامل - تطوير متقدم
            </small>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            usernameField.style.background = '#dcfce7';
            passwordField.style.background = '#dcfce7';
            
            setTimeout(() => {
                usernameField.style.background = '';
                passwordField.style.background = '';
            }, 1000);
        }

        // تأثير التحميل عند الإرسال
        document.getElementById('loginForm').addEventListener('submit', function() {
            const btn = document.getElementById('loginBtn');
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري تسجيل الدخول...';
            btn.disabled = true;
        });

        document.getElementById('username').focus();
    </script>
</body>
</html>
'''

# المسارات الأساسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        print(f"محاولة تسجيل دخول: {username}")  # للتشخيص

        if username in USERS and USERS[username]['password'] == password:
            session['user_id'] = username
            session['user_name'] = USERS[username]['name']
            session['user_role'] = USERS[username]['role']
            session['user_permissions'] = USERS[username]['permissions']
            flash(f'مرحباً {USERS[username]["name"]}!', 'success')
            print(f"تم تسجيل الدخول بنجاح: {username}")  # للتشخيص
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            print(f"فشل تسجيل الدخول: {username}")  # للتشخيص

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # حساب الإحصائيات
    total_invoices = len(SAMPLE_INVOICES)
    total_revenue = sum(inv['total_amount'] for inv in SAMPLE_INVOICES if inv['status'] == 'paid')
    total_expenses = sum(exp['amount'] for exp in SAMPLE_EXPENSES if exp['status'] == 'paid')
    net_profit = total_revenue - total_expenses

    # الأنشطة الحديثة
    recent_activities = [
        {
            'icon': 'fas fa-file-invoice',
            'title': 'فاتورة جديدة #INV-2024-0003',
            'description': 'للعميل: شركة الابتكار التقني',
            'time_ago': 'منذ ساعتين'
        },
        {
            'icon': 'fas fa-receipt',
            'title': 'مصروف جديد #EXP-2024-0018',
            'description': 'حملة إعلانية على جوجل',
            'time_ago': 'منذ 4 ساعات'
        },
        {
            'icon': 'fas fa-check-circle',
            'title': 'تم دفع فاتورة #INV-2024-0001',
            'description': 'مبلغ 25,000 ر.س',
            'time_ago': 'منذ يوم واحد'
        }
    ]

    dashboard_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 bg-gradient text-white" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="fw-bold mb-2">مرحباً {{ user_name }}</h2>
                                <p class="mb-0 opacity-75">إليك نظرة سريعة على أداء شركتك اليوم</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-chart-line fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stats-number">{{ total_invoices }}</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+12% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(total_revenue) }}</div>
                    <div class="text-muted">إجمالي الإيرادات (ر.س)</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+8% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(total_expenses) }}</div>
                    <div class="text-muted">إجمالي المصروفات (ر.س)</div>
                    <small class="text-danger">
                        <i class="fas fa-arrow-up me-1"></i>+5% من الشهر الماضي
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(net_profit) }}</div>
                    <div class="text-muted">صافي الربح (ر.س)</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>+15% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-8 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">الإيرادات والمصروفات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">الأنشطة الحديثة</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            {% for activity in recent_activities %}
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="{{ activity.icon }}"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">{{ activity.title }}</h6>
                                        <p class="text-muted mb-1 small">{{ activity.description }}</p>
                                        <small class="text-muted">{{ activity.time_ago }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2 text-primary"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <a href="/invoices/create" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                                    فاتورة جديدة
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/expenses/create" class="btn btn-outline-success w-100 py-3">
                                    <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                                    مصروف جديد
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/reports" class="btn btn-outline-info w-100 py-3">
                                    <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                    التقارير المالية
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 py-3" onclick="showDemo('settings')">
                                    <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                    الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const revenueCtx = document.getElementById('revenueChart').getContext('2d');
                new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: ['يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        datasets: [{
                            label: 'الإيرادات',
                            data: [18000, 22000, 19000, 28000, 25000, 32000],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'المصروفات',
                            data: [12000, 15000, 13000, 18000, 16000, 20000],
                            borderColor: '#059669',
                            backgroundColor: 'rgba(5, 150, 105, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'top' } },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' ر.س';
                                    }
                                }
                            }
                        }
                    }
                });
            });

            function showDemo(type) {
                alert('هذه الميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(dashboard_template,
                                user_name=session.get('user_name', 'مستخدم'),
                                user_role=session.get('user_role', 'مستخدم'),
                                total_invoices=total_invoices,
                                total_revenue=total_revenue,
                                total_expenses=total_expenses,
                                net_profit=net_profit,
                                recent_activities=recent_activities,
                                session=session)

@app.route('/invoices')
def invoices():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # إحصائيات الفواتير
    stats = {
        'total': len(SAMPLE_INVOICES),
        'paid': len([inv for inv in SAMPLE_INVOICES if inv['status'] == 'paid']),
        'sent': len([inv for inv in SAMPLE_INVOICES if inv['status'] == 'sent']),
        'overdue': len([inv for inv in SAMPLE_INVOICES if inv['status'] == 'overdue'])
    }

    invoices_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="text-white fw-bold">
                    <i class="fas fa-file-invoice me-2"></i>
                    إدارة الفواتير
                </h2>
                <p class="text-white-50">إدارة شاملة لجميع فواتير المبيعات والمشتريات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="/invoices/create" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    فاتورة جديدة
                </a>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stats-number">{{ stats.total }}</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number">{{ stats.paid }}</div>
                    <div class="text-muted">فواتير مدفوعة</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="stats-number">{{ stats.sent }}</div>
                    <div class="text-muted">فواتير مرسلة</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-number">{{ stats.overdue }}</div>
                    <div class="text-muted">فواتير متأخرة</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">قائمة الفواتير</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="showDemo('export')">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                                <button class="btn btn-outline-primary" onclick="showDemo('print')">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in invoices %}
                                    <tr>
                                        <td>
                                            <strong>{{ invoice.invoice_number }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ invoice.customer_name }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ invoice.issue_date }}</td>
                                        <td>{{ invoice.due_date }}</td>
                                        <td>
                                            <strong>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ invoice.status_color }}">
                                                {{ invoice.status_text }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="/invoices/{{ invoice.id }}" class="btn btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-outline-success" onclick="showDemo('edit')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="showDemo('print')" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                                {% if invoice.status != 'paid' %}
                                                <button class="btn btn-outline-danger" onclick="showDemo('delete')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            function showDemo(action) {
                const messages = {
                    'export': 'في النسخة الكاملة، ستتمكن من تصدير الفواتير إلى PDF و Excel!',
                    'print': 'في النسخة الكاملة، ستتمكن من طباعة الفواتير بتصميم احترافي!',
                    'edit': 'في النسخة الكاملة، ستتمكن من تعديل الفاتورة وإضافة/حذف البنود!',
                    'delete': 'في النسخة الكاملة، ستتمكن من حذف الفواتير غير المدفوعة!'
                };
                alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(invoices_template,
                                stats=stats,
                                invoices=SAMPLE_INVOICES,
                                session=session)

@app.route('/invoices/<int:invoice_id>')
def invoice_detail(invoice_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # البحث عن الفاتورة
    invoice = next((inv for inv in SAMPLE_INVOICES if inv['id'] == invoice_id), None)
    if not invoice:
        flash('الفاتورة غير موجودة', 'error')
        return redirect(url_for('invoices'))

    invoice_detail_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="text-white fw-bold">
                    <i class="fas fa-file-invoice me-2"></i>
                    تفاصيل الفاتورة {{ invoice.invoice_number }}
                </h2>
            </div>
            <div class="col-md-4 text-end">
                <a href="/invoices" class="btn btn-outline-light me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <button class="btn btn-primary" onclick="showDemo('print')">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">معلومات الفاتورة</h5>
                            <span class="badge bg-{{ invoice.status_color }} fs-6">{{ invoice.status_text }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">معلومات العميل</h6>
                                <p class="mb-1"><strong>{{ invoice.customer_name }}</strong></p>
                                <p class="text-muted mb-0">عميل معتمد</p>
                            </div>
                            <div class="col-md-6 text-end">
                                <h6 class="text-muted">تفاصيل الفاتورة</h6>
                                <p class="mb-1">تاريخ الإصدار: <strong>{{ invoice.issue_date }}</strong></p>
                                <p class="mb-0">تاريخ الاستحقاق: <strong>{{ invoice.due_date }}</strong></p>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in invoice['items'] %}
                                    <tr>
                                        <td>{{ item['description'] }}</td>
                                        <td>{{ item['quantity'] }}</td>
                                        <td>{{ "{:,.0f}".format(item['unit_price']) }} ر.س</td>
                                        <td><strong>{{ "{:,.0f}".format(item['total']) }} ر.س</strong></td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3" class="text-end">المجموع الفرعي:</th>
                                        <th>{{ "{:,.0f}".format(invoice.total_amount * 0.87) }} ر.س</th>
                                    </tr>
                                    <tr>
                                        <th colspan="3" class="text-end">ضريبة القيمة المضافة (15%):</th>
                                        <th>{{ "{:,.0f}".format(invoice.total_amount * 0.13) }} ر.س</th>
                                    </tr>
                                    <tr class="table-success">
                                        <th colspan="3" class="text-end">الإجمالي النهائي:</th>
                                        <th>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">إجراءات الفاتورة</h5>
                    </div>
                    <div class="card-body">
                        {% if invoice.status == 'sent' %}
                        <button class="btn btn-success w-100 mb-2" onclick="showDemo('mark_paid')">
                            <i class="fas fa-check me-2"></i>تسجيل كمدفوعة
                        </button>
                        {% endif %}

                        <button class="btn btn-primary w-100 mb-2" onclick="showDemo('send_email')">
                            <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                        </button>

                        <button class="btn btn-info w-100 mb-2" onclick="showDemo('download_pdf')">
                            <i class="fas fa-download me-2"></i>تحميل PDF
                        </button>

                        {% if invoice.status != 'paid' %}
                        <button class="btn btn-warning w-100 mb-2" onclick="showDemo('edit')">
                            <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                        </button>
                        {% endif %}

                        <hr>

                        <h6 class="text-muted">معلومات إضافية</h6>
                        <p class="small mb-1">تم الإنشاء: {{ invoice.issue_date }}</p>
                        <p class="small mb-1">آخر تحديث: {{ invoice.issue_date }}</p>
                        {% if invoice.status == 'paid' %}
                        <p class="small mb-0 text-success">تم الدفع: {{ invoice.issue_date }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            function showDemo(action) {
                const messages = {
                    'print': 'في النسخة الكاملة، ستتمكن من طباعة الفاتورة بتصميم احترافي!',
                    'mark_paid': 'في النسخة الكاملة، ستتمكن من تسجيل الفاتورة كمدفوعة!',
                    'send_email': 'في النسخة الكاملة، ستتمكن من إرسال الفاتورة بالبريد الإلكتروني!',
                    'download_pdf': 'في النسخة الكاملة، ستتمكن من تحميل الفاتورة كملف PDF!',
                    'edit': 'في النسخة الكاملة، ستتمكن من تعديل تفاصيل الفاتورة!'
                };
                alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(invoice_detail_template,
                                invoice=invoice,
                                session=session)

@app.route('/expenses')
def expenses():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    expenses_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="text-white fw-bold">
                    <i class="fas fa-receipt me-2"></i>
                    إدارة المصروفات
                </h2>
                <p class="text-white-50">تتبع وإدارة جميع مصروفات الشركة بطريقة منظمة</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-success btn-lg" onclick="showDemo('create')">
                    <i class="fas fa-plus me-2"></i>
                    مصروف جديد
                </button>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number">2</div>
                    <div class="text-muted">مصروفات مدفوعة</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number">2</div>
                    <div class="text-muted">في انتظار الموافقة</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number">15,800</div>
                    <div class="text-muted">إجمالي المصروفات (ر.س)</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stats-number">6</div>
                    <div class="text-muted">فئات المصروفات</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">قائمة المصروفات</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="showDemo('filter')">
                                    <i class="fas fa-filter me-1"></i>فلترة
                                </button>
                                <button class="btn btn-outline-success" onclick="showDemo('export')">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم المرجع</th>
                                        <th>العنوان</th>
                                        <th>الفئة</th>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in expenses %}
                                    <tr>
                                        <td>
                                            <strong>{{ expense.reference_number }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ expense.title }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ expense.category }}</span>
                                        </td>
                                        <td>{{ expense.vendor }}</td>
                                        <td>
                                            <strong>{{ "{:,.0f}".format(expense.amount) }} ر.س</strong>
                                        </td>
                                        <td>{{ expense.expense_date }}</td>
                                        <td>
                                            <span class="badge bg-{{ expense.status_color }}">
                                                {{ expense.status_text }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="showDemo('view')" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if expense.status == 'pending' %}
                                                <button class="btn btn-outline-success" onclick="showDemo('approve')" title="موافقة">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="showDemo('reject')" title="رفض">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                {% endif %}
                                                {% if expense.status == 'approved' %}
                                                <button class="btn btn-outline-info" onclick="showDemo('pay')" title="دفع">
                                                    <i class="fas fa-money-bill"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            function showDemo(action) {
                const messages = {
                    'create': 'في النسخة الكاملة، ستتمكن من إضافة مصروفات جديدة مع تصنيفها وإرفاق الإيصالات!',
                    'view': 'في النسخة الكاملة، ستتمكن من عرض تفاصيل المصروف والإيصالات المرفقة!',
                    'approve': 'في النسخة الكاملة، ستتمكن من الموافقة على المصروفات وإضافة ملاحظات!',
                    'reject': 'في النسخة الكاملة، ستتمكن من رفض المصروفات مع تحديد السبب!',
                    'pay': 'في النسخة الكاملة، ستتمكن من تسجيل دفع المصروفات وربطها بالحسابات!',
                    'filter': 'في النسخة الكاملة، ستتمكن من فلترة المصروفات حسب التاريخ والفئة والحالة!',
                    'export': 'في النسخة الكاملة، ستتمكن من تصدير المصروفات إلى Excel و PDF!'
                };
                alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(expenses_template,
                                expenses=SAMPLE_EXPENSES,
                                session=session)

@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    reports_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="text-white fw-bold">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير المالية
                </h2>
                <p class="text-white-50">تقارير شاملة ومفصلة لأداء شركتك المالي</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="/dashboard" class="btn btn-outline-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                </a>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-primary mb-3">
                            <i class="fas fa-file-invoice-dollar fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">تقرير الدخل</h5>
                        <p class="text-muted">قائمة الدخل والإيرادات والمصروفات</p>
                        <button class="btn btn-primary" onclick="showDemo('income')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-success mb-3">
                            <i class="fas fa-balance-scale fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">الميزانية العمومية</h5>
                        <p class="text-muted">الأصول والخصوم وحقوق الملكية</p>
                        <button class="btn btn-success" onclick="showDemo('balance')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-info mb-3">
                            <i class="fas fa-chart-line fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">التدفق النقدي</h5>
                        <p class="text-muted">حركة النقد الداخل والخارج</p>
                        <button class="btn btn-info" onclick="showDemo('cashflow')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-warning mb-3">
                            <i class="fas fa-chart-pie fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">تحليل المصروفات</h5>
                        <p class="text-muted">تفصيل المصروفات حسب الفئات</p>
                        <button class="btn btn-warning" onclick="showDemo('expenses')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-danger mb-3">
                            <i class="fas fa-users fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">تقرير الرواتب</h5>
                        <p class="text-muted">ملخص رواتب الموظفين والاستقطاعات</p>
                        <button class="btn btn-danger" onclick="showDemo('payroll')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card text-center h-100">
                    <div class="card-body p-4">
                        <div class="text-secondary mb-3">
                            <i class="fas fa-calendar-alt fa-3x"></i>
                        </div>
                        <h5 class="fw-bold">التقارير الدورية</h5>
                        <p class="text-muted">تقارير شهرية وربع سنوية وسنوية</p>
                        <button class="btn btn-secondary" onclick="showDemo('periodic')">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            معلومات التقارير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>في النسخة الكاملة من النظام:</h6>
                            <ul class="mb-0">
                                <li>تقارير مالية تفاعلية ومفصلة</li>
                                <li>إمكانية تصدير التقارير إلى PDF و Excel</li>
                                <li>تقارير مخصصة حسب الفترة الزمنية</li>
                                <li>رسوم بيانية متقدمة وتحليلات</li>
                                <li>مقارنات بين الفترات المختلفة</li>
                                <li>تقارير ضريبية متوافقة مع اللوائح السعودية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            function showDemo(type) {
                const messages = {
                    'income': 'تقرير الدخل: يعرض الإيرادات والمصروفات وصافي الربح للفترة المحددة',
                    'balance': 'الميزانية العمومية: تعرض الأصول والخصوم وحقوق الملكية في تاريخ محدد',
                    'cashflow': 'التدفق النقدي: يتتبع حركة النقد من الأنشطة التشغيلية والاستثمارية والتمويلية',
                    'expenses': 'تحليل المصروفات: يفصل المصروفات حسب الفئات مع نسب ومقارنات',
                    'payroll': 'تقرير الرواتب: يعرض ملخص رواتب الموظفين والاستقطاعات والمكافآت',
                    'periodic': 'التقارير الدورية: تقارير شهرية وربع سنوية وسنوية للمتابعة المستمرة'
                };

                alert('📊 ' + (messages[type] || 'تقرير مالي متقدم') + '\\n\\n🚀 هذه الميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(reports_template, session=session)

@app.route('/payroll')
def payroll():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # بيانات تجريبية للرواتب
    employees = [
        {
            'id': 1,
            'name': 'أحمد محمد علي',
            'position': 'مطور أول',
            'department': 'تقنية المعلومات',
            'basic_salary': 8000,
            'allowances': 1500,
            'deductions': 950,
            'net_salary': 8550,
            'status': 'active'
        },
        {
            'id': 2,
            'name': 'فاطمة أحمد السالم',
            'position': 'محاسبة رئيسية',
            'department': 'المالية',
            'basic_salary': 7000,
            'allowances': 1200,
            'deductions': 820,
            'net_salary': 7380,
            'status': 'active'
        },
        {
            'id': 3,
            'name': 'محمد عبدالله الزهراني',
            'position': 'مدير المبيعات',
            'department': 'المبيعات',
            'basic_salary': 9000,
            'allowances': 2000,
            'deductions': 1100,
            'net_salary': 9900,
            'status': 'active'
        }
    ]

    payroll_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="text-white fw-bold">
                    <i class="fas fa-users me-2"></i>
                    إدارة الرواتب
                </h2>
                <p class="text-white-50">نظام شامل لإدارة رواتب الموظفين والاستقطاعات</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-primary btn-lg" onclick="showDemo('process')">
                    <i class="fas fa-calculator me-2"></i>
                    معالجة الرواتب
                </button>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number">{{ employees|length }}</div>
                    <div class="text-muted">إجمالي الموظفين</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(employees|sum(attribute='basic_salary')) }}</div>
                    <div class="text-muted">إجمالي الرواتب الأساسية</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(employees|sum(attribute='allowances')) }}</div>
                    <div class="text-muted">إجمالي البدلات</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-minus-circle"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(employees|sum(attribute='deductions')) }}</div>
                    <div class="text-muted">إجمالي الاستقطاعات</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">كشف الرواتب - ديسمبر 2024</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="showDemo('export')">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                                <button class="btn btn-outline-success" onclick="showDemo('print')">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>المنصب</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الاستقطاعات</th>
                                        <th>صافي الراتب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for employee in employees %}
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ employee.name }}</strong>
                                                <br>
                                                <small class="text-muted">ID: {{ employee.id }}</small>
                                            </div>
                                        </td>
                                        <td>{{ employee.position }}</td>
                                        <td>{{ employee.department }}</td>
                                        <td>
                                            <strong>{{ "{:,.0f}".format(employee.basic_salary) }} ر.س</strong>
                                        </td>
                                        <td>
                                            <span class="text-success">+{{ "{:,.0f}".format(employee.allowances) }} ر.س</span>
                                        </td>
                                        <td>
                                            <span class="text-danger">-{{ "{:,.0f}".format(employee.deductions) }} ر.س</span>
                                        </td>
                                        <td>
                                            <strong class="text-primary">{{ "{:,.0f}".format(employee.net_salary) }} ر.س</strong>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="showDemo('view')" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="showDemo('slip')" title="كشف الراتب">
                                                    <i class="fas fa-file-alt"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="showDemo('edit')" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="3">الإجمالي:</th>
                                        <th>{{ "{:,.0f}".format(employees|sum(attribute='basic_salary')) }} ر.س</th>
                                        <th>{{ "{:,.0f}".format(employees|sum(attribute='allowances')) }} ر.س</th>
                                        <th>{{ "{:,.0f}".format(employees|sum(attribute='deductions')) }} ر.س</th>
                                        <th>{{ "{:,.0f}".format(employees|sum(attribute='net_salary')) }} ر.س</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2 text-primary"></i>
                            توزيع الرواتب حسب القسم
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="departmentChart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            معلومات الرواتب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>في النسخة الكاملة من النظام:</h6>
                            <ul class="mb-0">
                                <li>حساب تلقائي للرواتب والاستقطاعات</li>
                                <li>إدارة الإجازات والغياب</li>
                                <li>تقارير ضريبية وتأمينات اجتماعية</li>
                                <li>كشوف رواتب قابلة للطباعة</li>
                                <li>تكامل مع أنظمة البنوك</li>
                                <li>إشعارات تلقائية للموظفين</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''').replace('{% block extra_js %}{% endblock %}', '''
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const departmentCtx = document.getElementById('departmentChart').getContext('2d');
                new Chart(departmentCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['تقنية المعلومات', 'المالية', 'المبيعات'],
                        datasets: [{
                            data: [8550, 7380, 9900],
                            backgroundColor: ['#10b981', '#22c55e', '#84cc16']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom' },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed.toLocaleString() + ' ر.س';
                                    }
                                }
                            }
                        }
                    }
                });
            });

            function showDemo(action) {
                const messages = {
                    'process': 'في النسخة الكاملة، ستتمكن من معالجة الرواتب تلقائياً مع حساب الاستقطاعات!',
                    'view': 'في النسخة الكاملة، ستتمكن من عرض تفاصيل راتب الموظف والتاريخ الوظيفي!',
                    'slip': 'في النسخة الكاملة، ستتمكن من إنشاء وطباعة كشف راتب مفصل!',
                    'edit': 'في النسخة الكاملة، ستتمكن من تعديل بيانات الراتب والبدلات!',
                    'export': 'في النسخة الكاملة، ستتمكن من تصدير كشوف الرواتب إلى Excel!',
                    'print': 'في النسخة الكاملة، ستتمكن من طباعة تقارير الرواتب!'
                };
                alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
            }
        </script>
    ''')

    return render_template_string(payroll_template,
                                employees=employees,
                                session=session)

if __name__ == '__main__':
    print("🚀 تم تشغيل نظام المحاسبة المتكامل!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   الموظف: employee / emp123")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
