#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء نسخة عاملة من نظام المحاسبة
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_requirements():
    """إنشاء ملف requirements.txt"""
    
    requirements = '''flask==3.1.1
blinker>=1.9.0
click>=8.1.3
itsdangerous>=2.2.0
jinja2>=3.1.2
markupsafe>=2.1.1
werkzeug>=3.1.0
colorama
'''
    
    with open('requirements.txt', 'w') as f:
        f.write(requirements)
    
    print("✅ تم إنشاء ملف requirements.txt")

def create_simple_launcher():
    """إنشاء مشغل بسيط يعمل"""
    
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل بسيط لنظام المحاسبة
"""

import sys
import os
import time
import webbrowser
import threading
from pathlib import Path

def open_browser():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("✅ تم فتح المتصفح")
    except:
        print("⚠️  يرجى فتح المتصفح يدوياً: http://localhost:5000")

def main():
    print("=" * 60)
    print("           نظام المحاسبة المتكامل")
    print("         Integrated Accounting System")
    print("=" * 60)
    print()
    print("🚀 جاري تشغيل النظام...")
    print("🌐 سيفتح المتصفح على: http://localhost:5000")
    print()
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   مدير المتجر: manager / mgr123")
    print()
    print("⚠️  لإيقاف النظام: اضغط Ctrl+C")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # استيراد وتشغيل النظام
        from accounting_system_db import app
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('simple_launcher.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء المشغل البسيط")

def build_simple_exe():
    """بناء ملف تنفيذي بسيط"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['simple_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('accounting_system_db.py', '.'),
        ('accounting_system.db', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask.app',
        'flask.helpers',
        'flask.json',
        'jinja2',
        'jinja2.ext',
        'werkzeug',
        'werkzeug.serving',
        'werkzeug.utils',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'sqlite3',
        'datetime',
        'hashlib',
        'secrets',
        'json',
        'threading',
        'webbrowser',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AccountingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف .spec البسيط")
    
    # بناء الملف التنفيذي
    print("🔨 جاري بناء الملف التنفيذي...")
    
    try:
        result = subprocess.run([
            'py', '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'simple.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ خطأ في البناء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_desktop_launcher():
    """إنشاء مشغل سطح المكتب"""
    
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'
    if not desktop_path.exists():
        desktop_path = Path.home()
    
    # إنشاء ملف batch بسيط
    batch_content = f'''@echo off
title نظام المحاسبة
echo جاري تشغيل نظام المحاسبة...
cd /d "{Path('dist').absolute()}"
AccountingSystem.exe
pause
'''
    
    batch_file = desktop_path / 'نظام المحاسبة.bat'
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ تم إنشاء مشغل سطح المكتب: {batch_file}")

def main():
    """الدالة الرئيسية"""
    
    print("🔧 بناء نسخة عاملة من نظام المحاسبة")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    if not Path('accounting_system_db.py').exists():
        print("❌ ملف النظام غير موجود")
        return
    
    # إنشاء الملفات المطلوبة
    create_requirements()
    create_simple_launcher()
    
    # بناء الملف التنفيذي
    if build_simple_exe():
        # نسخ الملفات المهمة
        dist_path = Path('dist')
        if dist_path.exists():
            files_to_copy = [
                'accounting_system.db',
                'requirements.txt'
            ]
            
            for file_name in files_to_copy:
                if Path(file_name).exists():
                    shutil.copy2(file_name, dist_path)
                    print(f"📄 تم نسخ {file_name}")
            
            # إنشاء مشغل سطح المكتب
            create_desktop_launcher()
            
            print(f"\n📁 النظام جاهز في: {dist_path.absolute()}")
            print("🎉 يمكنك الآن تشغيل النظام!")
            
        else:
            print("❌ مجلد dist غير موجود")
    
    print("\n🎯 انتهى البناء!")

if __name__ == "__main__":
    main()
