#!/usr/bin/env python3
"""
نسخة tkinter من نظام المحاسبة
Tkinter Version of Accounting System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import webbrowser
import time
from accounting_system import app, USERS, SAMPLE_INVOICES, SAMPLE_EXPENSES

class AccountingSystemGUI:
    """واجهة نظام المحاسبة باستخدام tkinter"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام المحاسبة المتكامل")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f9ff')
        
        # متغيرات النظام
        self.current_user = None
        self.server_running = False
        
        # إعداد الخطوط العربية
        self.setup_fonts()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تشغيل خادم Flask في الخلفية
        self.start_flask_server()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        self.font_large = ('Arial', 14, 'bold')
        self.font_medium = ('Arial', 12)
        self.font_small = ('Arial', 10)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الشريط العلوي
        self.create_header()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء الشريط العلوي"""
        
        header_frame = tk.Frame(self.root, bg='#10b981', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            header_frame,
            text="🧮 نظام المحاسبة المتكامل",
            font=self.font_large,
            bg='#10b981',
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(header_frame, bg='#10b981')
        buttons_frame.pack(side='right', padx=20, pady=20)
        
        self.web_btn = tk.Button(
            buttons_frame,
            text="🌐 فتح في المتصفح",
            font=self.font_medium,
            bg='white',
            fg='#10b981',
            command=self.open_in_browser,
            padx=15,
            pady=5
        )
        self.web_btn.pack(side='right', padx=5)
        
        self.login_btn = tk.Button(
            buttons_frame,
            text="🔐 تسجيل الدخول",
            font=self.font_medium,
            bg='#059669',
            fg='white',
            command=self.show_login_dialog,
            padx=15,
            pady=5
        )
        self.login_btn.pack(side='right', padx=5)
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#f0f9ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إنشاء دفتر الملاحظات (Notebook)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب الفواتير
        self.create_invoices_tab()
        
        # تبويب المصروفات
        self.create_expenses_tab()
        
        # تبويب التقارير
        self.create_reports_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 لوحة التحكم")
        
        # إحصائيات سريعة
        stats_frame = tk.LabelFrame(
            dashboard_frame,
            text="الإحصائيات السريعة",
            font=self.font_medium,
            bg='white',
            padx=10,
            pady=10
        )
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        # بطاقات الإحصائيات
        stats_data = [
            ("📄 إجمالي الفواتير", len(SAMPLE_INVOICES), "#3b82f6"),
            ("💰 إجمالي الإيرادات", "125,000 ر.س", "#10b981"),
            ("💸 إجمالي المصروفات", "85,000 ر.س", "#ef4444"),
            ("📈 صافي الربح", "40,000 ر.س", "#059669")
        ]
        
        for i, (title, value, color) in enumerate(stats_data):
            col = i % 2
            row = i // 2
            
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            stat_frame.grid(row=row, column=col, padx=10, pady=5, sticky='ew')
            
            tk.Label(
                stat_frame,
                text=title,
                font=self.font_medium,
                bg=color,
                fg='white'
            ).pack(pady=5)
            
            tk.Label(
                stat_frame,
                text=str(value),
                font=self.font_large,
                bg=color,
                fg='white'
            ).pack(pady=5)
        
        # تكوين الأعمدة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # الإجراءات السريعة
        actions_frame = tk.LabelFrame(
            dashboard_frame,
            text="الإجراءات السريعة",
            font=self.font_medium,
            bg='white',
            padx=10,
            pady=10
        )
        actions_frame.pack(fill='x', padx=10, pady=10)
        
        actions = [
            ("📄 فاتورة جديدة", self.new_invoice),
            ("💸 مصروف جديد", self.new_expense),
            ("📊 عرض التقارير", self.show_reports),
            ("🌐 فتح النظام الكامل", self.open_in_browser)
        ]
        
        for i, (text, command) in enumerate(actions):
            btn = tk.Button(
                actions_frame,
                text=text,
                font=self.font_medium,
                bg='#10b981',
                fg='white',
                command=command,
                padx=20,
                pady=10
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='ew')
        
        actions_frame.grid_columnconfigure(0, weight=1)
        actions_frame.grid_columnconfigure(1, weight=1)
    
    def create_invoices_tab(self):
        """إنشاء تبويب الفواتير"""
        
        invoices_frame = ttk.Frame(self.notebook)
        self.notebook.add(invoices_frame, text="📄 الفواتير")
        
        # جدول الفواتير
        columns = ('رقم الفاتورة', 'العميل', 'المبلغ', 'الحالة')
        
        tree_frame = tk.Frame(invoices_frame)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.invoices_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        # تعريف الأعمدة
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=200)
        
        # إضافة البيانات
        for invoice in SAMPLE_INVOICES:
            self.invoices_tree.insert('', 'end', values=(
                invoice['invoice_number'],
                invoice['customer_name'],
                f"{invoice['total_amount']:,} ر.س",
                invoice['status_text']
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        self.invoices_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_expenses_tab(self):
        """إنشاء تبويب المصروفات"""
        
        expenses_frame = ttk.Frame(self.notebook)
        self.notebook.add(expenses_frame, text="💸 المصروفات")
        
        # جدول المصروفات
        columns = ('رقم المرجع', 'العنوان', 'المبلغ', 'الفئة', 'الحالة')
        
        tree_frame = tk.Frame(expenses_frame)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.expenses_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        # تعريف الأعمدة
        for col in columns:
            self.expenses_tree.heading(col, text=col)
            self.expenses_tree.column(col, width=150)
        
        # إضافة البيانات
        for expense in SAMPLE_EXPENSES:
            self.expenses_tree.insert('', 'end', values=(
                expense['reference_number'],
                expense['title'],
                f"{expense['amount']:,} ر.س",
                expense['category'],
                expense['status_text']
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.expenses_tree.yview)
        self.expenses_tree.configure(yscrollcommand=scrollbar.set)
        
        self.expenses_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")
        
        # قائمة التقارير
        reports_list = [
            "📈 تقرير الدخل",
            "⚖️ الميزانية العمومية", 
            "💸 التدفق النقدي",
            "🥧 تحليل المصروفات",
            "👥 تقرير الرواتب",
            "📅 التقارير الدورية"
        ]
        
        for i, report in enumerate(reports_list):
            btn = tk.Button(
                reports_frame,
                text=report,
                font=self.font_medium,
                bg='#3b82f6',
                fg='white',
                command=lambda r=report: self.show_report(r),
                padx=20,
                pady=15
            )
            btn.pack(fill='x', padx=20, pady=10)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        
        self.status_bar = tk.Label(
            self.root,
            text="جاهز - النظام يعمل على http://localhost:5000",
            font=self.font_small,
            bg='#e5e7eb',
            fg='#374151',
            relief='sunken',
            anchor='w'
        )
        self.status_bar.pack(side='bottom', fill='x')
    
    def start_flask_server(self):
        """تشغيل خادم Flask في الخلفية"""
        
        def run_server():
            try:
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
                self.server_running = True
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تشغيل الخادم: {e}")
        
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        
        # انتظار تشغيل الخادم
        self.root.after(2000, self.check_server_status)
    
    def check_server_status(self):
        """التحقق من حالة الخادم"""
        self.status_bar.config(text="✅ الخادم يعمل - النظام متاح على http://localhost:5000")
    
    def show_login_dialog(self):
        """عرض نافذة تسجيل الدخول"""
        
        login_window = tk.Toplevel(self.root)
        login_window.title("تسجيل الدخول")
        login_window.geometry("400x300")
        login_window.configure(bg='white')
        login_window.transient(self.root)
        login_window.grab_set()
        
        # عنوان
        tk.Label(
            login_window,
            text="🔐 تسجيل الدخول",
            font=self.font_large,
            bg='white'
        ).pack(pady=20)
        
        # حقول الإدخال
        tk.Label(login_window, text="اسم المستخدم:", font=self.font_medium, bg='white').pack()
        username_entry = tk.Entry(login_window, font=self.font_medium, width=20)
        username_entry.pack(pady=5)
        
        tk.Label(login_window, text="كلمة المرور:", font=self.font_medium, bg='white').pack()
        password_entry = tk.Entry(login_window, font=self.font_medium, width=20, show='*')
        password_entry.pack(pady=5)
        
        # أزرار المستخدمين السريعة
        tk.Label(login_window, text="أو اختر مستخدم:", font=self.font_small, bg='white').pack(pady=(20,5))
        
        users_frame = tk.Frame(login_window, bg='white')
        users_frame.pack()
        
        for username, data in USERS.items():
            btn = tk.Button(
                users_frame,
                text=f"{data['name']} ({username})",
                font=self.font_small,
                command=lambda u=username, p=data['password']: self.quick_login(u, p, login_window),
                bg='#10b981',
                fg='white',
                padx=10,
                pady=2
            )
            btn.pack(pady=2)
        
        # زر تسجيل الدخول
        def login():
            username = username_entry.get()
            password = password_entry.get()
            
            if username in USERS and USERS[username]['password'] == password:
                self.current_user = USERS[username]
                messagebox.showinfo("نجح", f"مرحباً {self.current_user['name']}!")
                login_window.destroy()
                self.update_user_status()
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        
        tk.Button(
            login_window,
            text="تسجيل الدخول",
            font=self.font_medium,
            bg='#10b981',
            fg='white',
            command=login,
            padx=20,
            pady=5
        ).pack(pady=20)
    
    def quick_login(self, username, password, window):
        """تسجيل دخول سريع"""
        self.current_user = USERS[username]
        messagebox.showinfo("نجح", f"مرحباً {self.current_user['name']}!")
        window.destroy()
        self.update_user_status()
    
    def update_user_status(self):
        """تحديث حالة المستخدم"""
        if self.current_user:
            self.status_bar.config(text=f"✅ مسجل الدخول: {self.current_user['name']} - النظام متاح على http://localhost:5000")
    
    def open_in_browser(self):
        """فتح النظام في المتصفح"""
        webbrowser.open('http://localhost:5000')
    
    def new_invoice(self):
        """فاتورة جديدة"""
        messagebox.showinfo("قريباً", "ميزة إنشاء فاتورة جديدة ستكون متاحة قريباً!\nيمكنك استخدام النسخة الكاملة في المتصفح.")
    
    def new_expense(self):
        """مصروف جديد"""
        messagebox.showinfo("قريباً", "ميزة إنشاء مصروف جديد ستكون متاحة قريباً!\nيمكنك استخدام النسخة الكاملة في المتصفح.")
    
    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("قريباً", "التقارير المفصلة متاحة في النسخة الكاملة في المتصفح!")
    
    def show_report(self, report_name):
        """عرض تقرير محدد"""
        messagebox.showinfo("تقرير", f"سيتم عرض {report_name} في النسخة الكاملة!")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الوظيفة الرئيسية"""
    
    print("🖥️ تشغيل نسخة سطح المكتب من نظام المحاسبة...")
    
    try:
        app_gui = AccountingSystemGUI()
        app_gui.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == '__main__':
    main()
