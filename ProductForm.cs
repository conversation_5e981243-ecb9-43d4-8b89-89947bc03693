using System;
using System.Data.SQLite;
using System.Drawing;
using System.Windows.Forms;

namespace AccountingSystem
{
    public partial class ProductForm : Form
    {
        private SQLiteConnection connection;
        private int? productId;
        
        private TextBox nameTextBox;
        private TextBox skuTextBox;
        private TextBox priceTextBox;
        private TextBox costTextBox;
        private TextBox stockTextBox;
        private TextBox minStockTextBox;
        private TextBox descriptionTextBox;
        
        public ProductForm(SQLiteConnection conn, int? id = null)
        {
            connection = conn;
            productId = id;
            InitializeComponent();
            
            if (productId.HasValue)
            {
                LoadProductData();
            }
        }
        
        private void InitializeComponent()
        {
            this.Text = productId.HasValue ? "تعديل منتج" : "إضافة منتج جديد";
            this.Size = new Size(450, 550);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // عنوان النافذة
            Label titleLabel = new Label
            {
                Text = "بيانات المنتج",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };
            
            // حقول النموذج
            int yPos = 60;
            int spacing = 50;
            
            // اسم المنتج
            Label nameLabel = new Label
            {
                Text = "اسم المنتج:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            nameTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // كود المنتج
            Label skuLabel = new Label
            {
                Text = "كود المنتج:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            skuTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // سعر البيع
            Label priceLabel = new Label
            {
                Text = "سعر البيع:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            priceTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // سعر التكلفة
            Label costLabel = new Label
            {
                Text = "سعر التكلفة:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            costTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // الكمية الحالية
            Label stockLabel = new Label
            {
                Text = "الكمية الحالية:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            stockTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // الحد الأدنى
            Label minStockLabel = new Label
            {
                Text = "الحد الأدنى:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            minStockTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 25),
                Font = new Font("Segoe UI", 9F)
            };
            
            yPos += spacing;
            
            // الوصف
            Label descLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, yPos),
                Size = new Size(100, 20)
            };
            descriptionTextBox = new TextBox
            {
                Location = new Point(130, yPos),
                Size = new Size(280, 60),
                Font = new Font("Segoe UI", 9F),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            
            yPos += 80;
            
            // أزرار الحفظ والإلغاء
            Button saveButton = new Button
            {
                Text = "حفظ",
                Size = new Size(80, 30),
                Location = new Point(330, yPos),
                BackColor = Color.FromArgb(58, 125, 68),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;
            
            Button cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 30),
                Location = new Point(240, yPos),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;
            
            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                titleLabel, nameLabel, nameTextBox, skuLabel, skuTextBox,
                priceLabel, priceTextBox, costLabel, costTextBox,
                stockLabel, stockTextBox, minStockLabel, minStockTextBox,
                descLabel, descriptionTextBox, saveButton, cancelButton
            });
        }
        
        private void LoadProductData()
        {
            string query = "SELECT * FROM products WHERE id = @id";
            using (var command = new SQLiteCommand(query, connection))
            {
                command.Parameters.AddWithValue("@id", productId.Value);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        nameTextBox.Text = reader.GetString("name");
                        skuTextBox.Text = reader.IsDBNull("sku") ? "" : reader.GetString("sku");
                        priceTextBox.Text = reader.GetDecimal("price").ToString("F2");
                        costTextBox.Text = reader.GetDecimal("cost").ToString("F2");
                        stockTextBox.Text = reader.GetInt32("stock_quantity").ToString();
                        minStockTextBox.Text = reader.GetInt32("min_stock_level").ToString();
                        descriptionTextBox.Text = reader.IsDBNull("description") ? "" : reader.GetString("description");
                    }
                }
            }
        }
        
        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من البيانات
                if (string.IsNullOrWhiteSpace(nameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nameTextBox.Focus();
                    return;
                }
                
                if (!decimal.TryParse(priceTextBox.Text, out decimal price) || price < 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    priceTextBox.Focus();
                    return;
                }
                
                if (!decimal.TryParse(costTextBox.Text, out decimal cost) || cost < 0)
                {
                    MessageBox.Show("يرجى إدخال تكلفة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    costTextBox.Focus();
                    return;
                }
                
                if (!int.TryParse(stockTextBox.Text, out int stock) || stock < 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    stockTextBox.Focus();
                    return;
                }
                
                if (!int.TryParse(minStockTextBox.Text, out int minStock) || minStock < 0)
                {
                    MessageBox.Show("يرجى إدخال حد أدنى صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    minStockTextBox.Focus();
                    return;
                }
                
                // حفظ البيانات
                string query;
                if (productId.HasValue)
                {
                    // تحديث المنتج
                    query = @"UPDATE products SET name = @name, sku = @sku, price = @price, 
                             cost = @cost, stock_quantity = @stock, min_stock_level = @minStock, 
                             description = @description WHERE id = @id";
                }
                else
                {
                    // إضافة منتج جديد
                    query = @"INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) 
                             VALUES (@name, @sku, @price, @cost, @stock, @minStock, @description)";
                }
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@name", nameTextBox.Text.Trim());
                    command.Parameters.AddWithValue("@sku", string.IsNullOrWhiteSpace(skuTextBox.Text) ? (object)DBNull.Value : skuTextBox.Text.Trim());
                    command.Parameters.AddWithValue("@price", price);
                    command.Parameters.AddWithValue("@cost", cost);
                    command.Parameters.AddWithValue("@stock", stock);
                    command.Parameters.AddWithValue("@minStock", minStock);
                    command.Parameters.AddWithValue("@description", string.IsNullOrWhiteSpace(descriptionTextBox.Text) ? (object)DBNull.Value : descriptionTextBox.Text.Trim());
                    
                    if (productId.HasValue)
                    {
                        command.Parameters.AddWithValue("@id", productId.Value);
                    }
                    
                    command.ExecuteNonQuery();
                }
                
                MessageBox.Show(productId.HasValue ? "تم تحديث المنتج بنجاح" : "تم إضافة المنتج بنجاح", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
