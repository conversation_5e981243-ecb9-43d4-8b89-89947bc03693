#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء النسخة النهائية من نظام المحاسبة
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    print("🏗️  Building Final Accounting System")
    print("=" * 50)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['accounting_system_db.py', 'accounting_standalone.py']
    for file_name in required_files:
        if not Path(file_name).exists():
            print(f"❌ Error: {file_name} not found")
            return
    
    # إنشاء ملف .spec محسن
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['accounting_standalone.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('accounting_system.db', '.'),
        ('accounting_system_db.py', '.'),
        ('remote_access_guide.md', '.'),
    ],
    hiddenimports=[
        'flask',
        'sqlite3',
        'datetime',
        'hashlib',
        'secrets',
        'json',
        'zipfile',
        'shutil',
        'psutil',
        'platform',
        'webbrowser',
        'threading',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AccountingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('accounting_final.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Created enhanced .spec file")
    
    # إنشاء ملف بدء التشغيل محسن
    startup_content = '''@echo off
chcp 65001 > nul
title نظام المحاسبة - Accounting System
cls
echo.
echo ================================================
echo           نظام المحاسبة المتكامل
echo        Integrated Accounting System
echo ================================================
echo.
echo جاري تشغيل النظام... Starting system...
echo.

REM تشغيل النظام
AccountingSystem.exe

echo.
echo تم إغلاق النظام - System closed
echo.
pause
'''
    
    with open('Start.bat', 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ Created enhanced startup script")
    
    # إنشاء ملف تعليمات شامل
    readme_content = '''# نظام المحاسبة المتكامل
# Integrated Accounting System

## 🚀 التشغيل السريع / Quick Start

### العربية:
1. انقر مرتين على "Start.bat"
2. انتظر حتى يفتح المتصفح تلقائياً
3. إذا لم يفتح، اذهب إلى: http://localhost:5000

### English:
1. Double-click "Start.bat"
2. Wait for browser to open automatically
3. If not opened, go to: http://localhost:5000

## 🔑 بيانات الدخول / Login Credentials

| المستخدم / User | اسم المستخدم / Username | كلمة المرور / Password |
|------------------|---------------------------|-------------------------|
| المدير / Admin   | admin                     | admin123                |
| المحاسب / Accountant | accountant            | acc123                  |
| مدير المتجر / Manager | manager               | mgr123                  |

## 📁 الملفات / Files

- `AccountingSystem.exe` - الملف التنفيذي / Executable file
- `accounting_system.db` - قاعدة البيانات / Database
- `Start.bat` - ملف التشغيل / Startup script
- `README.txt` - هذا الملف / This file

## ⚠️ تحذيرات مهمة / Important Warnings

### العربية:
- لا تحذف ملف قاعدة البيانات
- قم بنسخ احتياطية دورية
- أغلق النظام بشكل صحيح (Ctrl+C)
- لا تشغل أكثر من نسخة واحدة

### English:
- Don't delete the database file
- Make regular backups
- Close system properly (Ctrl+C)
- Don't run multiple instances

## 🔧 حل المشاكل / Troubleshooting

### المشكلة: النظام لا يعمل / Problem: System won't start
**الحل / Solution:** تشغيل كمدير / Run as Administrator

### المشكلة: المتصفح لا يفتح / Problem: Browser won't open
**الحل / Solution:** افتح يدوياً / Open manually: http://localhost:5000

### المشكلة: خطأ في المنفذ / Problem: Port error
**الحل / Solution:** تأكد من عدم استخدام المنفذ 5000 / Ensure port 5000 is free

## 🌐 الوصول عن بُعد / Remote Access

للدعم التقني، يمكن للمطور الوصول عن بُعد باستخدام:
For technical support, developer can access remotely using:

- رموز الوصول الآمنة / Secure access tokens
- API للصيانة / Maintenance API
- سجل شامل للعمليات / Comprehensive operation logs

## 📞 الدعم / Support

للحصول على الدعم التقني:
For technical support:

- وصف المشكلة / Describe the problem
- لقطة شاشة / Screenshot
- رسالة الخطأ / Error message

---
تم التطوير بواسطة فريق متخصص
Developed by Professional Team
'''
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Created comprehensive README")
    
    # بناء الملف التنفيذي
    print("🔨 Building final executable...")
    print("This may take several minutes...")
    
    try:
        result = subprocess.run([
            'py', '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'accounting_final.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Final executable built successfully!")
            
            # إنشاء مجلد التوزيع النهائي
            final_dist = Path('FinalRelease')
            if final_dist.exists():
                shutil.rmtree(final_dist)
            final_dist.mkdir()
            
            # نسخ الملفات من dist
            dist_path = Path('dist')
            if dist_path.exists():
                # نسخ الملف التنفيذي
                exe_file = dist_path / 'AccountingSystem.exe'
                if exe_file.exists():
                    shutil.copy2(exe_file, final_dist)
                    print("📄 Copied AccountingSystem.exe")
                
                # نسخ الملفات المهمة
                files_to_copy = [
                    'accounting_system.db',
                    'Start.bat',
                    'README.txt',
                    'remote_access_guide.md'
                ]
                
                for file_name in files_to_copy:
                    if Path(file_name).exists():
                        shutil.copy2(file_name, final_dist)
                        print(f"📄 Copied {file_name}")
                
                print(f"\n📁 Final release ready in: {final_dist.absolute()}")
                print("🎉 Ready for distribution!")
                print("\n📋 Release contents:")
                for item in final_dist.iterdir():
                    size = item.stat().st_size / (1024*1024)  # MB
                    print(f"  - {item.name} ({size:.1f} MB)")
            
        else:
            print("❌ Error during build:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n🎯 Final build process completed!")
    print("📦 The system is now ready for deployment to clients!")

if __name__ == "__main__":
    main()
