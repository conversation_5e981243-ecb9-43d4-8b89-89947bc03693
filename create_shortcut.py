#!/usr/bin/env python3
"""
إنشاء اختصار سطح المكتب لنظام المحاسبة
Create Desktop Shortcut for Accounting System
"""

import os
import sys

def create_windows_shortcut():
    """إنشاء اختصار Windows"""
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # مسار سطح المكتب
        desktop = winshell.desktop()
        
        # إنشاء الاختصار
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(os.path.join(desktop, 'نظام المحاسبة.lnk'))
        
        # إعدادات الاختصار
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{os.path.abspath("desktop_launcher.py")}"'
        shortcut.WorkingDirectory = os.path.abspath(".")
        shortcut.Description = "نظام المحاسبة المتكامل"
        
        # حفظ الاختصار
        shortcut.save()
        
        print("✅ تم إنشاء اختصار سطح المكتب بنجاح!")
        return True
        
    except ImportError:
        print("❌ مكتبات Windows غير متوفرة")
        print("💡 قم بتثبيت: pip install pywin32 winshell")
        return False
    except Exception as e:
        print(f"❌ فشل في إنشاء الاختصار: {e}")
        return False

def create_batch_file():
    """إنشاء ملف batch للتشغيل السريع"""
    
    batch_content = f'''@echo off
title نظام المحاسبة المتكامل
echo 🚀 تشغيل نظام المحاسبة...
echo.

cd /d "{os.path.abspath(".")}"

python desktop_launcher.py

pause
'''
    
    try:
        with open('تشغيل_نظام_المحاسبة.bat', 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print("✅ تم إنشاء ملف التشغيل السريع: تشغيل_نظام_المحاسبة.bat")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف batch: {e}")
        return False

def create_linux_desktop_file():
    """إنشاء ملف desktop لـ Linux"""
    
    desktop_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=نظام المحاسبة المتكامل
Name[en]=Accounting System
Comment=نظام محاسبة شامل
Comment[en]=Comprehensive Accounting System
Exec=python3 "{os.path.abspath("desktop_launcher.py")}"
Icon={os.path.abspath("icon.png")}
Path={os.path.abspath(".")}
Terminal=false
Categories=Office;Finance;
'''
    
    try:
        desktop_dir = os.path.expanduser("~/Desktop")
        desktop_file = os.path.join(desktop_dir, "accounting-system.desktop")
        
        with open(desktop_file, 'w', encoding='utf-8') as f:
            f.write(desktop_content)
        
        # جعل الملف قابل للتنفيذ
        os.chmod(desktop_file, 0o755)
        
        print("✅ تم إنشاء اختصار Linux بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء اختصار Linux: {e}")
        return False

def create_mac_app():
    """إنشاء تطبيق macOS"""
    
    app_dir = "نظام المحاسبة.app"
    contents_dir = os.path.join(app_dir, "Contents")
    macos_dir = os.path.join(contents_dir, "MacOS")
    resources_dir = os.path.join(contents_dir, "Resources")
    
    try:
        # إنشاء هيكل التطبيق
        os.makedirs(macos_dir, exist_ok=True)
        os.makedirs(resources_dir, exist_ok=True)
        
        # ملف Info.plist
        plist_content = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>accounting_system</string>
    <key>CFBundleIdentifier</key>
    <string>com.accounting.system</string>
    <key>CFBundleName</key>
    <string>نظام المحاسبة</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
</dict>
</plist>'''
        
        with open(os.path.join(contents_dir, "Info.plist"), 'w') as f:
            f.write(plist_content)
        
        # ملف التشغيل
        launcher_script = f'''#!/bin/bash
cd "{os.path.abspath(".")}"
python3 desktop_launcher.py
'''
        
        launcher_path = os.path.join(macos_dir, "accounting_system")
        with open(launcher_path, 'w') as f:
            f.write(launcher_script)
        
        # جعل الملف قابل للتنفيذ
        os.chmod(launcher_path, 0o755)
        
        print("✅ تم إنشاء تطبيق macOS بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء تطبيق macOS: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    
    print("🖥️ إنشاء اختصارات سطح المكتب لنظام المحاسبة")
    print("=" * 50)
    
    # تحديد نظام التشغيل
    if sys.platform.startswith('win'):
        print("🪟 نظام Windows محدد")
        
        # إنشاء ملف batch
        create_batch_file()
        
        # محاولة إنشاء اختصار Windows
        if not create_windows_shortcut():
            print("💡 يمكنك استخدام ملف: تشغيل_نظام_المحاسبة.bat")
    
    elif sys.platform.startswith('linux'):
        print("🐧 نظام Linux محدد")
        create_linux_desktop_file()
    
    elif sys.platform.startswith('darwin'):
        print("🍎 نظام macOS محدد")
        create_mac_app()
    
    else:
        print("❓ نظام تشغيل غير معروف")
        print("💡 يمكنك تشغيل النظام مباشرة: python desktop_launcher.py")
    
    print("\n🎉 تم الانتهاء من إنشاء الاختصارات!")
    print("\n📋 طرق تشغيل النظام:")
    print("1. اختصار سطح المكتب (إذا تم إنشاؤه)")
    print("2. ملف التشغيل السريع")
    print("3. الأمر: python desktop_launcher.py")

if __name__ == '__main__':
    main()
