<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <StartupObject>AccountingSystem.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>full</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ProductForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProductForm.Designer.cs">
      <DependentUpon>ProductForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CustomerForm.Designer.cs">
      <DependentUpon>CustomerForm.cs</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProductForm.resx">
      <DependentUpon>ProductForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomerForm.resx">
      <DependentUpon>CustomerForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

</Project>
