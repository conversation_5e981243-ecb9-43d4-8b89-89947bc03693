#!/usr/bin/env python3
"""
تشغيل نظام المحاسبة في بيئة الإنتاج
Production Server for Accounting System
"""

import os
import sys
from accounting_system import app

def run_production_server():
    """تشغيل النظام في وضع الإنتاج بدون تحذيرات"""
    
    print("🚀 تشغيل نظام المحاسبة في وضع الإنتاج...")
    print("=" * 60)
    
    # إعداد متغيرات البيئة للإنتاج
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = 'False'
    
    # تعطيل وضع التطوير
    app.config['DEBUG'] = False
    app.config['TESTING'] = False
    
    # إعدادات الأمان للإنتاج
    app.config['SESSION_COOKIE_SECURE'] = False  # True في HTTPS
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    print("✅ تم إعداد النظام للإنتاج")
    print("📱 النظام متاح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123") 
    print("   الموظف: employee / emp123")
    print("=" * 60)
    print("ℹ️  لن تظهر تحذيرات التطوير في هذا الوضع")
    print()
    
    try:
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

def install_production_server():
    """تثبيت خادم إنتاج متقدم"""
    
    print("📦 تثبيت خادم الإنتاج...")
    
    try:
        import subprocess
        
        # تثبيت Gunicorn
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'gunicorn'])
        print("✅ تم تثبيت Gunicorn بنجاح")
        
        print("\n🚀 يمكنك الآن تشغيل النظام باستخدام:")
        print("gunicorn -w 4 -b 0.0.0.0:5000 accounting_system:app")
        
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت Gunicorn")
        print("💡 يمكنك تثبيته يدوياً: pip install gunicorn")
    except ImportError:
        print("❌ subprocess غير متاح")

def run_with_gunicorn():
    """تشغيل النظام باستخدام Gunicorn"""
    
    try:
        import gunicorn
        print("🚀 تشغيل النظام باستخدام Gunicorn...")
        
        import subprocess
        cmd = [
            'gunicorn',
            '--workers', '4',
            '--bind', '0.0.0.0:5000',
            '--timeout', '120',
            '--keep-alive', '5',
            '--max-requests', '1000',
            '--preload',
            'accounting_system:app'
        ]
        
        subprocess.run(cmd)
        
    except ImportError:
        print("❌ Gunicorn غير مثبت")
        print("💡 قم بتثبيته أولاً: pip install gunicorn")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل Gunicorn: {e}")
        return False
    
    return True

def show_help():
    """عرض تعليمات الاستخدام"""
    
    print("📚 تعليمات تشغيل نظام المحاسبة")
    print("=" * 50)
    print()
    print("🔧 الخيارات المتاحة:")
    print()
    print("1️⃣  التشغيل العادي (مع تحذير التطوير):")
    print("   python accounting_system.py")
    print()
    print("2️⃣  التشغيل بدون تحذيرات:")
    print("   python production_server.py")
    print()
    print("3️⃣  التشغيل مع خادم إنتاج متقدم:")
    print("   python production_server.py --gunicorn")
    print()
    print("4️⃣  تثبيت خادم الإنتاج:")
    print("   python production_server.py --install")
    print()
    print("💡 ملاحظات:")
    print("   • التحذير الأحمر طبيعي في وضع التطوير")
    print("   • لا يؤثر على عمل النظام")
    print("   • يختفي عند استخدام خادم إنتاج")
    print()

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            show_help()
        elif sys.argv[1] == '--install':
            install_production_server()
        elif sys.argv[1] == '--gunicorn':
            if not run_with_gunicorn():
                print("\n🔄 التبديل للتشغيل العادي...")
                run_production_server()
        else:
            print("❌ خيار غير معروف. استخدم --help للمساعدة")
    else:
        run_production_server()
