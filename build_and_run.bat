@echo off
title بناء وتشغيل نظام المحاسبة C#
color 0A
cls
echo.
echo ================================================
echo           نظام المحاسبة - C# Desktop
echo        Accounting System - C# Desktop
echo ================================================
echo.

REM التحقق من وجود .NET
echo 🔍 فحص .NET Framework...
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo يرجى تثبيت .NET 6.0 أو أحدث من:
    echo https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET متوفر

REM بناء المشروع
echo.
echo 🔨 جاري بناء المشروع...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح

REM تشغيل التطبيق
echo.
echo 🚀 جاري تشغيل التطبيق...
echo.
echo 🔑 بيانات تسجيل الدخول:
echo    المدير: admin / admin123
echo    المحاسب: accountant / acc123
echo    مدير المتجر: manager / mgr123
echo.
echo ================================================
echo.

dotnet run --configuration Release

echo.
echo تم إغلاق التطبيق
pause
