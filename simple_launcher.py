#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل بسيط لنظام المحاسبة
"""

import sys
import os
import time
import webbrowser
import threading
from pathlib import Path

def open_browser():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("✅ تم فتح المتصفح")
    except:
        print("⚠️  يرجى فتح المتصفح يدوياً: http://localhost:5000")

def main():
    print("=" * 60)
    print("           نظام المحاسبة المتكامل")
    print("         Integrated Accounting System")
    print("=" * 60)
    print()
    print("🚀 جاري تشغيل النظام...")
    print("🌐 سيفتح المتصفح على: http://localhost:5000")
    print()
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   مدير المتجر: manager / mgr123")
    print()
    print("⚠️  لإيقاف النظام: اضغط Ctrl+C")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # استيراد وتشغيل النظام
        from accounting_system_db import app
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
