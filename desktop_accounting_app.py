#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة - تطبيق سطح المكتب
Desktop Accounting System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
from datetime import datetime, date
import hashlib
import json

class AccountingDesktopApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام المحاسبة المتكامل - Integrated Accounting System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات النظام
        self.current_user = None
        self.user_role = None
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إنشاء الواجهة
        self.create_login_interface()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.conn = sqlite3.connect('accounting_desktop.db')
        self.conn.row_factory = sqlite3.Row
        
        # إنشاء الجداول
        self.create_tables()
        self.insert_sample_data()
        
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                sku TEXT UNIQUE,
                price REAL NOT NULL,
                cost REAL NOT NULL,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                total_amount REAL NOT NULL,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول عناصر الفواتير
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                product_id INTEGER,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        self.conn.commit()
        
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        
        # التحقق من وجود المستخدمين
        users_count = self.conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
        
        if users_count == 0:
            # إضافة مستخدمين افتراضيين
            users = [
                ('admin', self.hash_password('admin123'), 'المدير العام', 'admin', '<EMAIL>'),
                ('accountant', self.hash_password('acc123'), 'المحاسب الرئيسي', 'accountant', '<EMAIL>'),
                ('manager', self.hash_password('mgr123'), 'مدير المبيعات', 'manager', '<EMAIL>')
            ]
            
            self.conn.executemany('''
                INSERT INTO users (username, password, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            ''', users)
            
            # إضافة منتجات تجريبية
            products = [
                ('لابتوب Dell', 'DELL001', 2500.00, 2000.00, 10, 2, 'لابتوب Dell Inspiron 15'),
                ('ماوس لاسلكي', 'MOUSE001', 50.00, 30.00, 25, 5, 'ماوس لاسلكي عالي الجودة'),
                ('كيبورد ميكانيكي', 'KB001', 150.00, 100.00, 15, 3, 'كيبورد ميكانيكي للألعاب'),
                ('شاشة 24 بوصة', 'MON001', 800.00, 600.00, 8, 2, 'شاشة LED 24 بوصة Full HD')
            ]
            
            self.conn.executemany('''
                INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', products)
            
            # إضافة عملاء تجريبيين
            customers = [
                ('شركة التقنية المتقدمة', '<EMAIL>', '0501234567', 'الرياض، المملكة العربية السعودية'),
                ('مؤسسة الأعمال الذكية', '<EMAIL>', '0507654321', 'جدة، المملكة العربية السعودية'),
                ('متجر الإلكترونيات الحديثة', '<EMAIL>', '0551234567', 'الدمام، المملكة العربية السعودية')
            ]
            
            self.conn.executemany('''
                INSERT INTO customers (name, email, phone, address)
                VALUES (?, ?, ?, ?)
            ''', customers)
            
            self.conn.commit()
            
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # إطار تسجيل الدخول
        login_frame = tk.Frame(self.root, bg='white', padx=50, pady=50)
        login_frame.place(relx=0.5, rely=0.5, anchor='center')
        
        # عنوان النظام
        title_label = tk.Label(login_frame, text="نظام المحاسبة المتكامل", 
                              font=('Arial', 24, 'bold'), bg='white', fg='#2E86AB')
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(login_frame, text="Integrated Accounting System", 
                                 font=('Arial', 14), bg='white', fg='#666')
        subtitle_label.pack(pady=(0, 30))
        
        # حقول تسجيل الدخول
        tk.Label(login_frame, text="اسم المستخدم:", font=('Arial', 12), bg='white').pack(anchor='w')
        self.username_entry = tk.Entry(login_frame, font=('Arial', 12), width=25)
        self.username_entry.pack(pady=(5, 15))
        
        tk.Label(login_frame, text="كلمة المرور:", font=('Arial', 12), bg='white').pack(anchor='w')
        self.password_entry = tk.Entry(login_frame, font=('Arial', 12), width=25, show='*')
        self.password_entry.pack(pady=(5, 20))
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_frame, text="تسجيل الدخول", font=('Arial', 12, 'bold'),
                             bg='#2E86AB', fg='white', width=20, command=self.login)
        login_btn.pack(pady=10)
        
        # معلومات تسجيل الدخول
        info_frame = tk.Frame(login_frame, bg='white')
        info_frame.pack(pady=20)
        
        tk.Label(info_frame, text="بيانات تسجيل الدخول التجريبية:", 
                font=('Arial', 10, 'bold'), bg='white').pack()
        tk.Label(info_frame, text="المدير: admin / admin123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        tk.Label(info_frame, text="المحاسب: accountant / acc123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        tk.Label(info_frame, text="المدير: manager / mgr123", 
                font=('Arial', 9), bg='white', fg='#666').pack()
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        # التحقق من بيانات المستخدم
        hashed_password = self.hash_password(password)
        user = self.conn.execute('''
            SELECT * FROM users WHERE username = ? AND password = ?
        ''', (username, hashed_password)).fetchone()
        
        if user:
            self.current_user = dict(user)
            self.user_role = user['role']
            self.create_main_interface()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # شريط القوائم
        self.create_menu_bar()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط المعلومات العلوي
        info_frame = tk.Frame(main_frame, bg='#2E86AB', height=60)
        info_frame.pack(fill='x', pady=(0, 10))
        info_frame.pack_propagate(False)
        
        welcome_label = tk.Label(info_frame, text=f"مرحباً، {self.current_user['full_name']}", 
                                font=('Arial', 14, 'bold'), bg='#2E86AB', fg='white')
        welcome_label.pack(side='left', padx=20, pady=15)
        
        time_label = tk.Label(info_frame, text=datetime.now().strftime("%Y-%m-%d %H:%M"), 
                             font=('Arial', 12), bg='#2E86AB', fg='white')
        time_label.pack(side='right', padx=20, pady=15)
        
        # منطقة المحتوى
        self.content_frame = tk.Frame(main_frame, bg='white')
        self.content_frame.pack(fill='both', expand=True)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة النظام
        system_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="النظام", menu=system_menu)
        system_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        system_menu.add_separator()
        system_menu.add_command(label="تسجيل الخروج", command=self.logout)
        system_menu.add_command(label="إغلاق", command=self.root.quit)
        
        # قائمة المنتجات
        products_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المنتجات", menu=products_menu)
        products_menu.add_command(label="عرض المنتجات", command=self.show_products)
        products_menu.add_command(label="إضافة منتج", command=self.add_product)
        
        # قائمة العملاء
        customers_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="العملاء", menu=customers_menu)
        customers_menu.add_command(label="عرض العملاء", command=self.show_customers)
        customers_menu.add_command(label="إضافة عميل", command=self.add_customer)
        
        # قائمة الفواتير
        invoices_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الفواتير", menu=invoices_menu)
        invoices_menu.add_command(label="عرض الفواتير", command=self.show_invoices)
        invoices_menu.add_command(label="إنشاء فاتورة", command=self.create_invoice)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
            
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content_frame()
        
        # عنوان لوحة التحكم
        title_label = tk.Label(self.content_frame, text="لوحة التحكم", 
                              font=('Arial', 18, 'bold'), bg='white')
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # حساب الإحصائيات
        products_count = self.conn.execute('SELECT COUNT(*) FROM products').fetchone()[0]
        customers_count = self.conn.execute('SELECT COUNT(*) FROM customers').fetchone()[0]
        invoices_count = self.conn.execute('SELECT COUNT(*) FROM invoices').fetchone()[0]
        total_sales = self.conn.execute('SELECT SUM(total_amount) FROM invoices').fetchone()[0] or 0
        
        # عرض الإحصائيات
        stats = [
            ("المنتجات", products_count, "#3A7D44"),
            ("العملاء", customers_count, "#2E86AB"),
            ("الفواتير", invoices_count, "#F39237"),
            ("إجمالي المبيعات", f"{total_sales:.2f} ر.س", "#A23B72")
        ]
        
        for i, (title, value, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, width=200, height=100)
            stat_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            stat_frame.pack_propagate(False)
            
            tk.Label(stat_frame, text=str(value), font=('Arial', 16, 'bold'), 
                    bg=color, fg='white').pack(expand=True)
            tk.Label(stat_frame, text=title, font=('Arial', 10), 
                    bg=color, fg='white').pack()
        
        # تكوين الأعمدة
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        stats_frame.grid_columnconfigure(2, weight=1)
        stats_frame.grid_columnconfigure(3, weight=1)
        
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.user_role = None
        self.create_login_interface()
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AccountingDesktopApp()
    app.run()
