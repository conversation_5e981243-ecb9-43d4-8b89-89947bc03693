#!/usr/bin/env python3
"""
تشغيل النظام المحاسبي - نسخة تجريبية
Run Accounting System - Demo Version
"""

import os
import sys
from datetime import date, timedelta

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, redirect, url_for, request, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات
app.config['SECRET_KEY'] = 'demo-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///demo_accounting.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نماذج مبسطة للعرض التجريبي
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=True)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def check_password(self, password):
        return self.password == password

class Company(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash(f'مرحباً {user.full_name}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('demo_login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/home')
def home():
    """الصفحة الرئيسية للعرض"""
    return render_template('demo_index_new.html')

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم التجريبية"""
    # بيانات تجريبية للعرض
    stats = {
        'total_invoices': 45,
        'total_revenue': 125000,
        'total_expenses': 85000,
        'net_profit': 40000
    }
    
    recent_activities = [
        {
            'icon': 'fas fa-file-invoice',
            'title': 'فاتورة جديدة #INV-2024-0001',
            'description': 'للعميل: شركة التقنية المتقدمة',
            'time_ago': 'منذ ساعتين'
        },
        {
            'icon': 'fas fa-receipt',
            'title': 'مصروف جديد #EXP-2024-0015',
            'description': 'إيجار المكتب - شهر ديسمبر',
            'time_ago': 'منذ 4 ساعات'
        },
        {
            'icon': 'fas fa-users',
            'title': 'كشف راتب جديد',
            'description': 'رواتب شهر ديسمبر 2024',
            'time_ago': 'منذ يوم واحد'
        }
    ]
    
    pending_tasks = {
        'overdue_invoices': 3,
        'pending_approvals': 5,
        'completed_today': 8,
        'due_this_week': 12
    }
    
    return render_template('demo_dashboard.html', 
                         stats=stats,
                         recent_activities=recent_activities,
                         pending_tasks=pending_tasks)

@app.route('/invoices')
@login_required
def invoices():
    """صفحة الفواتير التجريبية"""
    # بيانات فواتير تجريبية
    invoices = [
        {
            'id': 1,
            'invoice_number': 'INV-2024-0001',
            'customer_name': 'شركة التقنية المتقدمة',
            'total_amount': 25000,
            'status': 'paid',
            'status_text': 'مدفوعة',
            'status_color': 'success',
            'issue_date': '2024-12-01',
            'due_date': '2024-12-31'
        },
        {
            'id': 2,
            'invoice_number': 'INV-2024-0002',
            'customer_name': 'مؤسسة الحلول الذكية',
            'total_amount': 18500,
            'status': 'sent',
            'status_text': 'مرسلة',
            'status_color': 'info',
            'issue_date': '2024-12-15',
            'due_date': '2025-01-15'
        },
        {
            'id': 3,
            'invoice_number': 'INV-2024-0003',
            'customer_name': 'شركة الابتكار التقني',
            'total_amount': 32000,
            'status': 'overdue',
            'status_text': 'متأخرة',
            'status_color': 'danger',
            'issue_date': '2024-11-20',
            'due_date': '2024-12-20'
        }
    ]
    
    stats = {
        'total': 45,
        'draft': 5,
        'sent': 12,
        'paid': 25,
        'overdue': 3
    }
    
    return render_template('demo_invoices.html', invoices=invoices, stats=stats)

@app.route('/expenses')
def expenses():
    """صفحة المصروفات التجريبية"""
    expenses = [
        {
            'id': 1,
            'reference_number': 'EXP-2024-0015',
            'title': 'إيجار المكتب - شهر ديسمبر',
            'amount': 12000,
            'category': 'الإيجار',
            'status': 'paid',
            'status_text': 'مدفوع',
            'status_color': 'success',
            'expense_date': '2024-12-01'
        },
        {
            'id': 2,
            'reference_number': 'EXP-2024-0016',
            'title': 'فاتورة الكهرباء',
            'amount': 850,
            'category': 'المرافق',
            'status': 'approved',
            'status_text': 'موافق عليه',
            'status_color': 'info',
            'expense_date': '2024-12-28'
        },
        {
            'id': 3,
            'reference_number': 'EXP-2024-0017',
            'title': 'مستلزمات مكتبية',
            'amount': 450,
            'category': 'المكتبية',
            'status': 'pending',
            'status_text': 'في انتظار الموافقة',
            'status_color': 'warning',
            'expense_date': '2024-12-28'
        }
    ]
    
    return render_template('demo_expenses.html', expenses=expenses)

@app.route('/reports')
def reports():
    """صفحة التقارير التجريبية"""
    return render_template('demo_reports.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء بيانات تجريبية إذا لم تكن موجودة
        if not User.query.first():
            # مستخدم مدير
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='أحمد',
                last_name='المدير'
            )
            db.session.add(admin_user)

            # مستخدم محاسب
            accountant_user = User(
                username='accountant',
                email='<EMAIL>',
                password='acc123',
                first_name='فاطمة',
                last_name='المحاسبة'
            )
            db.session.add(accountant_user)

            # مستخدم موظف
            employee_user = User(
                username='employee',
                email='<EMAIL>',
                password='emp123',
                first_name='محمد',
                last_name='الموظف'
            )
            db.session.add(employee_user)
            
            demo_company = Company(
                name='شركة المحاسبة التجريبية',
                email='<EMAIL>',
                phone='+************'
            )
            db.session.add(demo_company)
            db.session.commit()
    
    print("🚀 تم تشغيل النظام المحاسبي التجريبي!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("👤 للدخول للوحة التحكم: http://localhost:5000/dashboard")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
