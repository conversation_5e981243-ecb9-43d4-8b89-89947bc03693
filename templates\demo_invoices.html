{% extends "demo_base.html" %}

{% block title %}الفواتير - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="text-white fw-bold">
            <i class="fas fa-file-invoice me-2"></i>
            إدارة الفواتير
        </h2>
        <p class="text-white-50">إدارة شاملة لجميع فواتير المبيعات والمشتريات</p>
    </div>
    <div class="col-md-4 text-end">
        <button class="btn btn-primary btn-lg" onclick="showDemo('create')">
            <i class="fas fa-plus me-2"></i>
            فاتورة جديدة
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="stats-number">{{ stats.total }}</div>
            <div class="text-muted">إجمالي الفواتير</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">{{ stats.paid }}</div>
            <div class="text-muted">فواتير مدفوعة</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-paper-plane"></i>
            </div>
            <div class="stats-number">{{ stats.sent }}</div>
            <div class="text-muted">فواتير مرسلة</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number">{{ stats.overdue }}</div>
            <div class="text-muted">فواتير متأخرة</div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" placeholder="رقم الفاتورة أو اسم العميل">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="draft">مسودة</option>
                            <option value="sent">مرسلة</option>
                            <option value="paid">مدفوعة</option>
                            <option value="overdue">متأخرة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">النوع</label>
                        <select class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="sales">فاتورة مبيعات</option>
                            <option value="purchase">فاتورة مشتريات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <button class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoices Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة الفواتير</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <strong>{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ invoice.customer_name }}</strong>
                                    </div>
                                </td>
                                <td>{{ invoice.issue_date }}</td>
                                <td>{{ invoice.due_date }}</td>
                                <td>
                                    <strong>{{ "{:,.0f}".format(invoice.total_amount) }} ر.س</strong>
                                </td>
                                <td>
                                    <span class="badge bg-{{ invoice.status_color }}">
                                        {{ invoice.status_text }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="showDemo('view')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="showDemo('edit')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="showDemo('print')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        {% if invoice.status != 'paid' %}
                                        <button class="btn btn-outline-danger" onclick="showDemo('delete')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">عرض 1-3 من أصل 45 فاتورة</small>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">السابق</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Preview Modal -->
<div class="modal fade" id="invoicePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-file-invoice fa-5x text-primary mb-3"></i>
                    <h4>معاينة الفاتورة</h4>
                    <p class="text-muted">في النسخة الكاملة، ستظهر هنا تفاصيل الفاتورة كاملة</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">طباعة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showDemo(action) {
    const messages = {
        'create': 'في النسخة الكاملة، ستتمكن من إنشاء فواتير جديدة مع إضافة البنود والعملاء!',
        'view': 'في النسخة الكاملة، ستتمكن من عرض تفاصيل الفاتورة كاملة!',
        'edit': 'في النسخة الكاملة، ستتمكن من تعديل الفاتورة وإضافة/حذف البنود!',
        'print': 'في النسخة الكاملة، ستتمكن من طباعة الفاتورة بتصميم احترافي!',
        'delete': 'في النسخة الكاملة، ستتمكن من حذف الفواتير غير المدفوعة!'
    };
    
    alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
}

// تأثيرات تفاعلية للجدول
document.addEventListener('DOMContentLoaded', function() {
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'all 0.2s ease';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>
{% endblock %}
