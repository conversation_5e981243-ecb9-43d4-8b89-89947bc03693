#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء اختصار بسيط على سطح المكتب
"""

import os
import sys
import shutil
from pathlib import Path

def create_desktop_files():
    """إنشاء ملفات سطح المكتب"""
    
    print("🖥️  إنشاء ملفات سطح المكتب")
    print("=" * 40)
    
    # التحقق من وجود الملف التنفيذي
    exe_path = Path('FinalRelease/AccountingSystem.exe').absolute()
    if not exe_path.exists():
        print("❌ لم يتم العثور على الملف التنفيذي")
        print("تأكد من تشغيل build_final.py أولاً")
        return False
    
    # الحصول على مسار سطح المكتب
    desktop_path = Path.home() / 'Desktop'
    if not desktop_path.exists():
        desktop_path = Path.home() / 'سطح المكتب'  # للنسخة العربية
    
    if not desktop_path.exists():
        print("❌ لم يتم العثور على مجلد سطح المكتب")
        desktop_path = Path.home()  # استخدام المجلد الرئيسي
        print(f"📁 سيتم الإنشاء في: {desktop_path}")
    
    # إنشاء ملف batch للتشغيل السريع
    batch_content = f'''@echo off
cd /d "{exe_path.parent}"
start "" "{exe_path.name}"
'''
    
    batch_file = desktop_path / 'تشغيل نظام المحاسبة.bat'
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ تم إنشاء ملف التشغيل: {batch_file}")
    
    # إنشاء ملف PowerShell للتشغيل المتقدم
    ps_content = f'''# نظام المحاسبة المتكامل
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "           نظام المحاسبة المتكامل" -ForegroundColor Yellow
Write-Host "        Integrated Accounting System" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 جاري تشغيل النظام..." -ForegroundColor Green
Write-Host "🌐 سيفتح المتصفح تلقائياً على: http://localhost:5000" -ForegroundColor Blue
Write-Host ""

# تغيير المجلد
Set-Location "{exe_path.parent}"

# تشغيل النظام
Start-Process -FilePath "{exe_path.name}" -NoNewWindow

# انتظار قليل ثم فتح المتصفح
Start-Sleep -Seconds 3
Start-Process "http://localhost:5000"

Write-Host "✅ تم تشغيل النظام بنجاح!" -ForegroundColor Green
Write-Host "📱 بيانات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "   المدير: admin / admin123" -ForegroundColor White
Write-Host "   المحاسب: accountant / acc123" -ForegroundColor White
Write-Host "   مدير المتجر: manager / mgr123" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  لإيقاف النظام: أغلق نافذة المتصفح واضغط Ctrl+C" -ForegroundColor Red
'''
    
    ps_file = desktop_path / 'تشغيل نظام المحاسبة.ps1'
    with open(ps_file, 'w', encoding='utf-8') as f:
        f.write(ps_content)
    
    print(f"✅ تم إنشاء ملف PowerShell: {ps_file}")
    
    # إنشاء ملف تعليمات سطح المكتب
    instructions_content = '''# تشغيل نظام المحاسبة على سطح المكتب

## 🚀 طرق التشغيل

### الطريقة الأولى (الأسهل):
1. انقر مرتين على "تشغيل نظام المحاسبة.bat"
2. سيفتح النظام تلقائياً

### الطريقة الثانية (متقدمة):
1. انقر بالزر الأيمن على "تشغيل نظام المحاسبة.ps1"
2. اختر "Run with PowerShell"
3. سيفتح النظام مع معلومات إضافية

### الطريقة الثالثة (مباشرة):
1. اذهب إلى مجلد FinalRelease
2. انقر مرتين على "Start.bat"

## 🔑 بيانات تسجيل الدخول

- المدير: admin / admin123
- المحاسب: accountant / acc123  
- مدير المتجر: manager / mgr123

## 📱 رابط النظام

بعد التشغيل، اذهب إلى: http://localhost:5000

## ⚠️ ملاحظات مهمة

- تأكد من عدم تشغيل أكثر من نسخة واحدة
- لإيقاف النظام: اضغط Ctrl+C في نافذة الأوامر
- احتفظ بنسخة احتياطية من قاعدة البيانات

---
للدعم التقني، راجع ملف README.txt في مجلد FinalRelease
'''
    
    instructions_file = desktop_path / 'تعليمات تشغيل نظام المحاسبة.txt'
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions_content)
    
    print(f"✅ تم إنشاء ملف التعليمات: {instructions_file}")
    
    # نسخ مجلد FinalRelease إلى سطح المكتب (اختياري)
    desktop_release = desktop_path / 'نظام المحاسبة'
    if desktop_release.exists():
        shutil.rmtree(desktop_release)
    
    try:
        shutil.copytree('FinalRelease', desktop_release)
        print(f"✅ تم نسخ النظام إلى سطح المكتب: {desktop_release}")
    except Exception as e:
        print(f"⚠️  لم يتم نسخ النظام: {e}")
    
    return True

def create_taskbar_shortcut():
    """إنشاء اختصار لشريط المهام"""
    
    try:
        # إنشاء ملف VBS لتشغيل النظام بدون نافذة أوامر
        vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{Path('FinalRelease').absolute()}"
WshShell.Run "AccountingSystem.exe", 0, False

' فتح المتصفح بعد 3 ثوان
WScript.Sleep 3000
WshShell.Run "http://localhost:5000"
'''
        
        desktop_path = Path.home() / 'Desktop'
        if not desktop_path.exists():
            desktop_path = Path.home() / 'سطح المكتب'
        if not desktop_path.exists():
            desktop_path = Path.home()
        
        vbs_file = desktop_path / 'نظام المحاسبة (صامت).vbs'
        with open(vbs_file, 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        
        print(f"✅ تم إنشاء ملف التشغيل الصامت: {vbs_file}")
        print("💡 هذا الملف يشغل النظام بدون إظهار نافذة الأوامر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف الصامت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    # إنشاء ملفات سطح المكتب
    if create_desktop_files():
        print("\n🎉 تم إنشاء جميع ملفات سطح المكتب بنجاح!")
        
        # إنشاء الملف الصامت
        create_taskbar_shortcut()
        
        print("\n📋 الملفات المُنشأة على سطح المكتب:")
        print("1. 'تشغيل نظام المحاسبة.bat' - للتشغيل السريع")
        print("2. 'تشغيل نظام المحاسبة.ps1' - للتشغيل المتقدم")
        print("3. 'نظام المحاسبة (صامت).vbs' - للتشغيل بدون نافذة")
        print("4. 'تعليمات تشغيل نظام المحاسبة.txt' - التعليمات")
        print("5. مجلد 'نظام المحاسبة' - نسخة كاملة من النظام")
        
        print("\n🚀 كيفية الاستخدام:")
        print("• للتشغيل السريع: انقر مرتين على الملف .bat")
        print("• للتشغيل الصامت: انقر مرتين على الملف .vbs")
        print("• للتعليمات: افتح ملف .txt")
        
    else:
        print("\n❌ فشل في إنشاء ملفات سطح المكتب")

if __name__ == "__main__":
    main()
