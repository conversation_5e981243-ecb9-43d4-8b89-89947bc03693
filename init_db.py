#!/usr/bin/env python3
"""
سكريبت تهيئة قاعدة البيانات
Database Initialization Script
"""

import os
import sys
from datetime import date, timedelta

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from app.models import (
    db, User, UserRole, Company, Account, AccountType,
    Invoice, InvoiceItem, InvoiceType, InvoiceStatus,
    Expense, ExpenseCategory, ExpenseStatus,
    Employee, EmployeeStatus, Payroll, PayrollStatus
)

def init_database():
    """تهيئة قاعدة البيانات"""
    print("🔄 جاري تهيئة قاعدة البيانات...")
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # إنشاء شركة تجريبية
        company = create_sample_company()
        
        # إنشاء مستخدم مدير
        admin_user = create_admin_user(company.id)
        
        # إنشاء الحسابات الافتراضية
        create_default_accounts(company.id)
        
        # إنشاء فئات المصروفات الافتراضية
        create_default_expense_categories(company.id)
        
        # إنشاء بيانات تجريبية
        create_sample_data(company.id, admin_user.id)
        
        print("🎉 تم إعداد قاعدة البيانات بنجاح!")
        print(f"👤 المستخدم المدير: admin")
        print(f"🔑 كلمة المرور: admin123")

def create_sample_company():
    """إنشاء شركة تجريبية"""
    company = Company(
        name="شركة التقنية المتقدمة",
        name_en="Advanced Technology Company",
        commercial_register="**********",
        tax_number="***************",
        email="<EMAIL>",
        phone="+************",
        address_line1="شارع الملك فهد",
        address_line2="حي العليا",
        city="الرياض",
        state="منطقة الرياض",
        postal_code="12345",
        country="المملكة العربية السعودية",
        currency="SAR",
        default_tax_rate=0.15
    )
    
    db.session.add(company)
    db.session.commit()
    print(f"✅ تم إنشاء الشركة: {company.name}")
    return company

def create_admin_user(company_id):
    """إنشاء مستخدم مدير"""
    admin = User(
        username="admin",
        email="<EMAIL>",
        password="admin123",
        first_name="أحمد",
        last_name="المدير",
        role=UserRole.ADMIN
    )
    admin.company_id = company_id
    admin.is_active = True
    admin.email_verified = True
    
    db.session.add(admin)
    db.session.commit()
    print(f"✅ تم إنشاء المستخدم المدير: {admin.username}")
    return admin

def create_default_accounts(company_id):
    """إنشاء الحسابات المحاسبية الافتراضية"""
    Account.create_default_accounts(company_id)
    print("✅ تم إنشاء الحسابات المحاسبية الافتراضية")

def create_default_expense_categories(company_id):
    """إنشاء فئات المصروفات الافتراضية"""
    Expense.create_default_categories(company_id)
    print("✅ تم إنشاء فئات المصروفات الافتراضية")

def create_sample_data(company_id, user_id):
    """إنشاء بيانات تجريبية"""
    print("🔄 جاري إنشاء البيانات التجريبية...")
    
    # إنشاء موظفين تجريبيين
    create_sample_employees(company_id)
    
    # إنشاء فواتير تجريبية
    create_sample_invoices(company_id, user_id)
    
    # إنشاء مصروفات تجريبية
    create_sample_expenses(company_id, user_id)
    
    print("✅ تم إنشاء البيانات التجريبية")

def create_sample_employees(company_id):
    """إنشاء موظفين تجريبيين"""
    employees_data = [
        {
            'first_name': 'محمد',
            'last_name': 'أحمد',
            'national_id': '1234567890',
            'job_title': 'مطور برمجيات',
            'basic_salary': 8000,
            'housing_allowance': 2000,
            'transportation_allowance': 500,
            'email': '<EMAIL>',
            'phone': '+966501234567'
        },
        {
            'first_name': 'فاطمة',
            'last_name': 'علي',
            'national_id': '1234567891',
            'job_title': 'محاسبة',
            'basic_salary': 7000,
            'housing_allowance': 1800,
            'transportation_allowance': 500,
            'email': '<EMAIL>',
            'phone': '+966501234568'
        },
        {
            'first_name': 'خالد',
            'last_name': 'سعد',
            'national_id': '1234567892',
            'job_title': 'مدير مبيعات',
            'basic_salary': 9000,
            'housing_allowance': 2200,
            'transportation_allowance': 600,
            'email': '<EMAIL>',
            'phone': '+966501234569'
        }
    ]
    
    for emp_data in employees_data:
        employee = Employee(
            first_name=emp_data['first_name'],
            last_name=emp_data['last_name'],
            national_id=emp_data['national_id'],
            job_title=emp_data['job_title'],
            basic_salary=emp_data['basic_salary'],
            hire_date=date.today() - timedelta(days=365),
            company_id=company_id
        )
        employee.housing_allowance = emp_data['housing_allowance']
        employee.transportation_allowance = emp_data['transportation_allowance']
        employee.email = emp_data['email']
        employee.phone = emp_data['phone']
        
        db.session.add(employee)
    
    db.session.commit()

def create_sample_invoices(company_id, user_id):
    """إنشاء فواتير تجريبية"""
    customers = [
        'شركة الأعمال المتقدمة',
        'مؤسسة التقنية الحديثة',
        'شركة الحلول الذكية',
        'مجموعة الابتكار التقني'
    ]
    
    for i, customer in enumerate(customers):
        # فاتورة مبيعات
        invoice = Invoice(
            invoice_type=InvoiceType.SALES,
            customer_name=customer,
            company_id=company_id,
            created_by_id=user_id,
            due_date=date.today() + timedelta(days=30)
        )
        invoice.customer_email = f"contact@customer{i+1}.com"
        invoice.customer_phone = f"+96650123456{i}"
        invoice.status = InvoiceStatus.PAID if i < 2 else InvoiceStatus.SENT
        
        if invoice.status == InvoiceStatus.PAID:
            invoice.paid_date = date.today() - timedelta(days=i*5)
        
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID
        
        # إضافة بنود الفاتورة
        items_data = [
            ('تطوير نظام إدارة المحتوى', 1, 15000),
            ('استشارات تقنية', 20, 500),
            ('صيانة وتطوير', 1, 5000)
        ]
        
        for desc, qty, price in items_data[:2]:  # أول بندين فقط
            item = InvoiceItem(
                description=desc,
                quantity=qty,
                unit_price=price,
                invoice_id=invoice.id
            )
            db.session.add(item)
        
        invoice.calculate_totals()
        
        if invoice.status == InvoiceStatus.PAID:
            invoice.paid_amount = invoice.total_amount
    
    db.session.commit()

def create_sample_expenses(company_id, user_id):
    """إنشاء مصروفات تجريبية"""
    categories = ExpenseCategory.query.filter_by(company_id=company_id).all()
    
    expenses_data = [
        ('إيجار المكتب - شهر ديسمبر', 12000, 'الإيجار'),
        ('فاتورة الكهرباء', 800, 'المرافق'),
        ('مستلزمات مكتبية', 450, 'المكتبية'),
        ('حملة إعلانية على جوجل', 2500, 'التسويق والإعلان'),
        ('دورة تدريبية للموظفين', 3000, 'التدريب')
    ]
    
    for title, amount, cat_name in expenses_data:
        category = next((c for c in categories if c.name == cat_name), categories[0])
        
        expense = Expense(
            title=title,
            amount=amount,
            category_id=category.id,
            company_id=company_id,
            created_by_id=user_id
        )
        expense.status = ExpenseStatus.PAID
        expense.paid_date = date.today() - timedelta(days=len(expenses_data))
        expense.vendor_name = f"مورد {title.split()[0]}"
        
        db.session.add(expense)
    
    db.session.commit()

if __name__ == '__main__':
    init_database()
