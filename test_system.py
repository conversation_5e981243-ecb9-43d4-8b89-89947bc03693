#!/usr/bin/env python3
"""
اختبارات نظام المحاسبة المتكامل
Accounting System Tests
"""

import unittest
import sys
import os

# إضافة مسار المشروع للاستيراد
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accounting_system import app, USERS, SAMPLE_INVOICES, SAMPLE_EXPENSES

class AccountingSystemTests(unittest.TestCase):
    """اختبارات النظام المحاسبي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.app = app.test_client()
        self.app.testing = True
    
    def test_home_redirect(self):
        """اختبار إعادة التوجيه من الصفحة الرئيسية"""
        response = self.app.get('/')
        self.assertEqual(response.status_code, 302)  # إعادة توجيه
        self.assertIn('/login', response.location)
    
    def test_login_page_loads(self):
        """اختبار تحميل صفحة تسجيل الدخول"""
        response = self.app.get('/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn('تسجيل الدخول', response.data.decode('utf-8'))
        self.assertIn('نظام المحاسبة المتكامل', response.data.decode('utf-8'))
    
    def test_valid_login(self):
        """اختبار تسجيل دخول صحيح"""
        response = self.app.post('/login', data={
            'username': 'admin',
            'password': 'admin123'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('مرحباً أحمد المدير', response.data.decode('utf-8'))
    
    def test_invalid_login(self):
        """اختبار تسجيل دخول خاطئ"""
        response = self.app.post('/login', data={
            'username': 'wrong_user',
            'password': 'wrong_password'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('اسم المستخدم أو كلمة المرور غير صحيحة', response.data.decode('utf-8'))
    
    def test_dashboard_requires_login(self):
        """اختبار أن لوحة التحكم تتطلب تسجيل دخول"""
        response = self.app.get('/dashboard')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/login', response.location)
    
    def test_dashboard_with_login(self):
        """اختبار لوحة التحكم مع تسجيل دخول"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/dashboard')
        self.assertEqual(response.status_code, 200)
        self.assertIn('لوحة التحكم', response.data.decode('utf-8'))
        self.assertIn('مرحباً أحمد المدير', response.data.decode('utf-8'))
    
    def test_invoices_page(self):
        """اختبار صفحة الفواتير"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/invoices')
        self.assertEqual(response.status_code, 200)
        self.assertIn('إدارة الفواتير', response.data.decode('utf-8'))
        self.assertIn('INV-2024-0001', response.data.decode('utf-8'))
    
    def test_invoice_detail(self):
        """اختبار تفاصيل الفاتورة"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/invoices/1')
        self.assertEqual(response.status_code, 200)
        self.assertIn('تفاصيل الفاتورة', response.data.decode('utf-8'))
        self.assertIn('شركة التقنية المتقدمة', response.data.decode('utf-8'))
    
    def test_invoice_not_found(self):
        """اختبار فاتورة غير موجودة"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/invoices/999')
        self.assertEqual(response.status_code, 302)  # إعادة توجيه
    
    def test_expenses_page(self):
        """اختبار صفحة المصروفات"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/expenses')
        self.assertEqual(response.status_code, 200)
        self.assertIn('إدارة المصروفات', response.data.decode('utf-8'))
        self.assertIn('EXP-2024-0015', response.data.decode('utf-8'))
    
    def test_reports_page(self):
        """اختبار صفحة التقارير"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/reports')
        self.assertEqual(response.status_code, 200)
        self.assertIn('التقارير المالية', response.data.decode('utf-8'))
        self.assertIn('تقرير الدخل', response.data.decode('utf-8'))
    
    def test_payroll_page(self):
        """اختبار صفحة الرواتب"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/payroll')
        self.assertEqual(response.status_code, 200)
        self.assertIn('إدارة الرواتب', response.data.decode('utf-8'))
        self.assertIn('أحمد محمد علي', response.data.decode('utf-8'))
    
    def test_logout(self):
        """اختبار تسجيل الخروج"""
        # تسجيل الدخول أولاً
        with self.app.session_transaction() as sess:
            sess['user_id'] = 'admin'
            sess['user_name'] = 'أحمد المدير'
            sess['user_role'] = 'مدير'
        
        response = self.app.get('/logout', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn('تم تسجيل الخروج بنجاح', response.data.decode('utf-8'))
    
    def test_users_data_integrity(self):
        """اختبار سلامة بيانات المستخدمين"""
        self.assertIn('admin', USERS)
        self.assertIn('accountant', USERS)
        self.assertIn('employee', USERS)
        
        # التحقق من وجود البيانات المطلوبة
        for user_id, user_data in USERS.items():
            self.assertIn('password', user_data)
            self.assertIn('name', user_data)
            self.assertIn('role', user_data)
            self.assertIn('permissions', user_data)
    
    def test_sample_data_integrity(self):
        """اختبار سلامة البيانات التجريبية"""
        # اختبار بيانات الفواتير
        self.assertGreater(len(SAMPLE_INVOICES), 0)
        for invoice in SAMPLE_INVOICES:
            self.assertIn('id', invoice)
            self.assertIn('invoice_number', invoice)
            self.assertIn('customer_name', invoice)
            self.assertIn('total_amount', invoice)
            self.assertIn('status', invoice)
        
        # اختبار بيانات المصروفات
        self.assertGreater(len(SAMPLE_EXPENSES), 0)
        for expense in SAMPLE_EXPENSES:
            self.assertIn('id', expense)
            self.assertIn('reference_number', expense)
            self.assertIn('title', expense)
            self.assertIn('amount', expense)
            self.assertIn('status', expense)

class DataCalculationTests(unittest.TestCase):
    """اختبارات العمليات الحسابية"""
    
    def test_invoice_totals(self):
        """اختبار حساب إجماليات الفواتير"""
        paid_invoices = [inv for inv in SAMPLE_INVOICES if inv['status'] == 'paid']
        total_revenue = sum(inv['total_amount'] for inv in paid_invoices)
        
        self.assertGreater(total_revenue, 0)
        self.assertEqual(len(paid_invoices), 1)  # فاتورة واحدة مدفوعة في البيانات التجريبية
    
    def test_expense_totals(self):
        """اختبار حساب إجماليات المصروفات"""
        paid_expenses = [exp for exp in SAMPLE_EXPENSES if exp['status'] == 'paid']
        total_expenses = sum(exp['amount'] for exp in paid_expenses)
        
        self.assertGreater(total_expenses, 0)
        self.assertEqual(len(paid_expenses), 2)  # مصروفان مدفوعان في البيانات التجريبية
    
    def test_profit_calculation(self):
        """اختبار حساب صافي الربح"""
        total_revenue = sum(inv['total_amount'] for inv in SAMPLE_INVOICES if inv['status'] == 'paid')
        total_expenses = sum(exp['amount'] for exp in SAMPLE_EXPENSES if exp['status'] == 'paid')
        net_profit = total_revenue - total_expenses
        
        self.assertIsInstance(net_profit, (int, float))
        # في البيانات التجريبية، يجب أن يكون هناك ربح
        self.assertGreater(net_profit, 0)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء تشغيل اختبارات نظام المحاسبة المتكامل...")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات النظام
    test_suite.addTest(unittest.makeSuite(AccountingSystemTests))
    test_suite.addTest(unittest.makeSuite(DataCalculationTests))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        print(f"📊 تم تشغيل {result.testsRun} اختبار بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"📊 نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"📊 فشل: {len(result.failures)}")
        print(f"📊 أخطاء: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
