using System;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace AccountingSystem
{
    public partial class MainForm : Form
    {
        private SQLiteConnection connection;
        private User currentUser;
        private Panel contentPanel;
        private Label welcomeLabel;
        
        public MainForm()
        {
            InitializeComponent();
            InitializeDatabase();
            ShowLoginInterface();
        }
        
        private void InitializeComponent()
        {
            // إعداد النافذة الرئيسية
            this.Text = "نظام المحاسبة المتكامل - Integrated Accounting System";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            
            // منع تغيير الحجم
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
        }
        
        private void InitializeDatabase()
        {
            try
            {
                string connectionString = "Data Source=accounting_desktop.db;Version=3;";
                connection = new SQLiteConnection(connectionString);
                connection.Open();
                
                CreateTables();
                InsertSampleData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void CreateTables()
        {
            string[] createTableQueries = {
                @"CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    sku TEXT UNIQUE,
                    price REAL NOT NULL,
                    cost REAL NOT NULL,
                    stock_quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL,
                    tax_amount REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    status TEXT DEFAULT 'pending',
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )"
            };
            
            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }
        
        private void InsertSampleData()
        {
            // التحقق من وجود المستخدمين
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM users", connection))
            {
                int userCount = Convert.ToInt32(command.ExecuteScalar());
                
                if (userCount == 0)
                {
                    // إضافة مستخدمين افتراضيين
                    string[] userInserts = {
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('admin', '{HashPassword("admin123")}', 'المدير العام', 'admin', '<EMAIL>')",
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('accountant', '{HashPassword("acc123")}', 'المحاسب الرئيسي', 'accountant', '<EMAIL>')",
                        $"INSERT INTO users (username, password, full_name, role, email) VALUES ('manager', '{HashPassword("mgr123")}', 'مدير المبيعات', 'manager', '<EMAIL>')"
                    };
                    
                    foreach (string insert in userInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                    
                    // إضافة منتجات تجريبية
                    string[] productInserts = {
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('لابتوب Dell', 'DELL001', 2500.00, 2000.00, 10, 2, 'لابتوب Dell Inspiron 15')",
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('ماوس لاسلكي', 'MOUSE001', 50.00, 30.00, 25, 5, 'ماوس لاسلكي عالي الجودة')",
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('كيبورد ميكانيكي', 'KB001', 150.00, 100.00, 15, 3, 'كيبورد ميكانيكي للألعاب')",
                        "INSERT INTO products (name, sku, price, cost, stock_quantity, min_stock_level, description) VALUES ('شاشة 24 بوصة', 'MON001', 800.00, 600.00, 8, 2, 'شاشة LED 24 بوصة Full HD')"
                    };
                    
                    foreach (string insert in productInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                    
                    // إضافة عملاء تجريبيين
                    string[] customerInserts = {
                        "INSERT INTO customers (name, email, phone, address) VALUES ('شركة التقنية المتقدمة', '<EMAIL>', '0501234567', 'الرياض، المملكة العربية السعودية')",
                        "INSERT INTO customers (name, email, phone, address) VALUES ('مؤسسة الأعمال الذكية', '<EMAIL>', '0507654321', 'جدة، المملكة العربية السعودية')",
                        "INSERT INTO customers (name, email, phone, address) VALUES ('متجر الإلكترونيات الحديثة', '<EMAIL>', '0551234567', 'الدمام، المملكة العربية السعودية')"
                    };
                    
                    foreach (string insert in customerInserts)
                    {
                        using (var cmd = new SQLiteCommand(insert, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }
                }
            }
        }
        
        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        
        private void ShowLoginInterface()
        {
            // مسح المحتوى الحالي
            this.Controls.Clear();
            
            // إنشاء panel تسجيل الدخول
            Panel loginPanel = new Panel
            {
                Size = new Size(400, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // توسيط panel
            loginPanel.Location = new Point(
                (this.ClientSize.Width - loginPanel.Width) / 2,
                (this.ClientSize.Height - loginPanel.Height) / 2
            );
            
            // عنوان النظام
            Label titleLabel = new Label
            {
                Text = "نظام المحاسبة المتكامل",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Size = new Size(350, 40),
                Location = new Point(25, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label subtitleLabel = new Label
            {
                Text = "Integrated Accounting System",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.Gray,
                Size = new Size(350, 25),
                Location = new Point(25, 75),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // حقول تسجيل الدخول
            Label usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 25),
                Location = new Point(30, 130)
            };
            
            TextBox usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(300, 25),
                Location = new Point(30, 155),
                Name = "usernameTextBox"
            };
            
            Label passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10F),
                Size = new Size(100, 25),
                Location = new Point(30, 190)
            };
            
            TextBox passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Size = new Size(300, 25),
                Location = new Point(30, 215),
                UseSystemPasswordChar = true,
                Name = "passwordTextBox"
            };
            
            // زر تسجيل الدخول
            Button loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(300, 35),
                Location = new Point(30, 260),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            loginButton.Click += (sender, e) => Login(usernameTextBox.Text, passwordTextBox.Text);
            
            // معلومات تسجيل الدخول
            Label infoLabel = new Label
            {
                Text = "بيانات تسجيل الدخول التجريبية:\nالمدير: admin / admin123\nالمحاسب: accountant / acc123\nالمدير: manager / mgr123",
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Gray,
                Size = new Size(300, 80),
                Location = new Point(30, 320)
            };
            
            // إضافة العناصر للـ panel
            loginPanel.Controls.AddRange(new Control[] {
                titleLabel, subtitleLabel, usernameLabel, usernameTextBox,
                passwordLabel, passwordTextBox, loginButton, infoLabel
            });
            
            // إضافة panel للنافذة
            this.Controls.Add(loginPanel);
            
            // ربط Enter بتسجيل الدخول
            passwordTextBox.KeyPress += (sender, e) => {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    Login(usernameTextBox.Text, passwordTextBox.Text);
                }
            };
        }
        
        private void Login(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            string hashedPassword = HashPassword(password);
            string query = "SELECT * FROM users WHERE username = @username AND password = @password";
            
            using (var command = new SQLiteCommand(query, connection))
            {
                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@password", hashedPassword);
                
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        currentUser = new User
                        {
                            Id = reader.GetInt32("id"),
                            Username = reader.GetString("username"),
                            FullName = reader.GetString("full_name"),
                            Role = reader.GetString("role"),
                            Email = reader.IsDBNull("email") ? "" : reader.GetString("email")
                        };
                        
                        ShowMainInterface();
                    }
                    else
                    {
                        MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        
        private void ShowMainInterface()
        {
            // مسح المحتوى الحالي
            this.Controls.Clear();
            
            // إنشاء شريط القوائم
            CreateMenuBar();
            
            // إنشاء شريط المعلومات العلوي
            CreateTopInfoBar();
            
            // إنشاء منطقة المحتوى
            contentPanel = new Panel
            {
                Location = new Point(10, 80),
                Size = new Size(this.ClientSize.Width - 20, this.ClientSize.Height - 90),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            this.Controls.Add(contentPanel);
            
            // عرض لوحة التحكم افتراضياً
            ShowDashboard();
        }
        
        private void CreateMenuBar()
        {
            MenuStrip menuStrip = new MenuStrip
            {
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F)
            };
            
            // قائمة النظام
            ToolStripMenuItem systemMenu = new ToolStripMenuItem("النظام");
            systemMenu.DropDownItems.Add("لوحة التحكم", null, (s, e) => ShowDashboard());
            systemMenu.DropDownItems.Add(new ToolStripSeparator());
            systemMenu.DropDownItems.Add("تسجيل الخروج", null, (s, e) => Logout());
            systemMenu.DropDownItems.Add("إغلاق", null, (s, e) => this.Close());
            
            // قائمة المنتجات
            ToolStripMenuItem productsMenu = new ToolStripMenuItem("المنتجات");
            productsMenu.DropDownItems.Add("عرض المنتجات", null, (s, e) => ShowProducts());
            productsMenu.DropDownItems.Add("إضافة منتج", null, (s, e) => AddProduct());
            
            // قائمة العملاء
            ToolStripMenuItem customersMenu = new ToolStripMenuItem("العملاء");
            customersMenu.DropDownItems.Add("عرض العملاء", null, (s, e) => ShowCustomers());
            customersMenu.DropDownItems.Add("إضافة عميل", null, (s, e) => AddCustomer());
            
            // قائمة الفواتير
            ToolStripMenuItem invoicesMenu = new ToolStripMenuItem("الفواتير");
            invoicesMenu.DropDownItems.Add("عرض الفواتير", null, (s, e) => ShowInvoices());
            invoicesMenu.DropDownItems.Add("إنشاء فاتورة", null, (s, e) => CreateInvoice());
            
            // قائمة التقارير
            ToolStripMenuItem reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("تقرير المبيعات", null, (s, e) => SalesReport());
            reportsMenu.DropDownItems.Add("تقرير المخزون", null, (s, e) => InventoryReport());
            
            menuStrip.Items.AddRange(new ToolStripItem[] {
                systemMenu, productsMenu, customersMenu, invoicesMenu, reportsMenu
            });
            
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }
        
        private void CreateTopInfoBar()
        {
            Panel infoPanel = new Panel
            {
                Location = new Point(10, 30),
                Size = new Size(this.ClientSize.Width - 20, 45),
                BackColor = Color.FromArgb(46, 134, 171)
            };
            
            welcomeLabel = new Label
            {
                Text = $"مرحباً، {currentUser.FullName}",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 12),
                AutoSize = true
            };
            
            Label timeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(infoPanel.Width - 150, 15),
                AutoSize = true
            };
            
            infoPanel.Controls.AddRange(new Control[] { welcomeLabel, timeLabel });
            this.Controls.Add(infoPanel);
        }
        
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();

            // عنوان لوحة التحكم
            Label titleLabel = new Label
            {
                Text = "لوحة التحكم",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            contentPanel.Controls.Add(titleLabel);

            // إحصائيات النظام
            CreateDashboardStats();
        }

        private void CreateDashboardStats()
        {
            // حساب الإحصائيات
            int productsCount = GetCount("products");
            int customersCount = GetCount("customers");
            int invoicesCount = GetCount("invoices");
            decimal totalSales = GetTotalSales();

            // إنشاء بطاقات الإحصائيات
            var stats = new[]
            {
                new { Title = "المنتجات", Value = productsCount.ToString(), Color = Color.FromArgb(58, 125, 68) },
                new { Title = "العملاء", Value = customersCount.ToString(), Color = Color.FromArgb(46, 134, 171) },
                new { Title = "الفواتير", Value = invoicesCount.ToString(), Color = Color.FromArgb(243, 146, 55) },
                new { Title = "إجمالي المبيعات", Value = $"{totalSales:F2} ر.س", Color = Color.FromArgb(162, 59, 114) }
            };

            for (int i = 0; i < stats.Length; i++)
            {
                Panel statPanel = new Panel
                {
                    Size = new Size(200, 100),
                    Location = new Point(20 + (i * 220), 70),
                    BackColor = stats[i].Color
                };

                Label valueLabel = new Label
                {
                    Text = stats[i].Value,
                    Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(10, 25),
                    AutoSize = true
                };

                Label titleLabel = new Label
                {
                    Text = stats[i].Title,
                    Font = new Font("Segoe UI", 9F),
                    ForeColor = Color.White,
                    Location = new Point(10, 55),
                    AutoSize = true
                };

                statPanel.Controls.AddRange(new Control[] { valueLabel, titleLabel });
                contentPanel.Controls.Add(statPanel);
            }
        }

        private int GetCount(string tableName)
        {
            using (var command = new SQLiteCommand($"SELECT COUNT(*) FROM {tableName}", connection))
            {
                return Convert.ToInt32(command.ExecuteScalar());
            }
        }

        private decimal GetTotalSales()
        {
            using (var command = new SQLiteCommand("SELECT COALESCE(SUM(total_amount), 0) FROM invoices", connection))
            {
                return Convert.ToDecimal(command.ExecuteScalar());
            }
        }

        private void ShowProducts()
        {
            contentPanel.Controls.Clear();

            // عنوان الصفحة
            Label titleLabel = new Label
            {
                Text = "إدارة المنتجات",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            // زر إضافة منتج
            Button addButton = new Button
            {
                Text = "إضافة منتج جديد",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(150, 30),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(58, 125, 68),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += (s, e) => AddProduct();

            // جدول المنتجات
            DataGridView productsGrid = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(contentPanel.Width - 40, contentPanel.Height - 120),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // تحميل بيانات المنتجات
            LoadProductsData(productsGrid);

            // ربط النقر المزدوج للتعديل
            productsGrid.CellDoubleClick += (s, e) => {
                if (e.RowIndex >= 0)
                {
                    int productId = Convert.ToInt32(productsGrid.Rows[e.RowIndex].Cells["id"].Value);
                    EditProduct(productId);
                }
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, addButton, productsGrid });
        }

        private void LoadProductsData(DataGridView grid)
        {
            string query = @"SELECT id, name, sku, price, cost, stock_quantity, min_stock_level
                           FROM products ORDER BY name";

            using (var adapter = new SQLiteDataAdapter(query, connection))
            {
                DataTable dataTable = new DataTable();
                adapter.Fill(dataTable);

                // تعيين أسماء الأعمدة بالعربية
                dataTable.Columns["id"].ColumnName = "المعرف";
                dataTable.Columns["name"].ColumnName = "اسم المنتج";
                dataTable.Columns["sku"].ColumnName = "الكود";
                dataTable.Columns["price"].ColumnName = "السعر";
                dataTable.Columns["cost"].ColumnName = "التكلفة";
                dataTable.Columns["stock_quantity"].ColumnName = "المخزون";
                dataTable.Columns["min_stock_level"].ColumnName = "الحد الأدنى";

                grid.DataSource = dataTable;

                // إخفاء عمود المعرف
                if (grid.Columns["المعرف"] != null)
                    grid.Columns["المعرف"].Visible = false;

                // تلوين المنتجات منخفضة المخزون
                foreach (DataGridViewRow row in grid.Rows)
                {
                    int stock = Convert.ToInt32(row.Cells["المخزون"].Value);
                    int minStock = Convert.ToInt32(row.Cells["الحد الأدنى"].Value);

                    if (stock <= minStock)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 204, 204);
                    }
                }
            }
        }

        private void ShowCustomers()
        {
            contentPanel.Controls.Clear();

            // عنوان الصفحة
            Label titleLabel = new Label
            {
                Text = "إدارة العملاء",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            // زر إضافة عميل
            Button addButton = new Button
            {
                Text = "إضافة عميل جديد",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(150, 30),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += (s, e) => AddCustomer();

            // جدول العملاء
            DataGridView customersGrid = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(contentPanel.Width - 40, contentPanel.Height - 120),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // تحميل بيانات العملاء
            LoadCustomersData(customersGrid);

            // ربط النقر المزدوج للتعديل
            customersGrid.CellDoubleClick += (s, e) => {
                if (e.RowIndex >= 0)
                {
                    int customerId = Convert.ToInt32(customersGrid.Rows[e.RowIndex].Cells["id"].Value);
                    EditCustomer(customerId);
                }
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, addButton, customersGrid });
        }

        private void LoadCustomersData(DataGridView grid)
        {
            string query = "SELECT id, name, email, phone, address FROM customers ORDER BY name";

            using (var adapter = new SQLiteDataAdapter(query, connection))
            {
                DataTable dataTable = new DataTable();
                adapter.Fill(dataTable);

                // تعيين أسماء الأعمدة بالعربية
                dataTable.Columns["id"].ColumnName = "المعرف";
                dataTable.Columns["name"].ColumnName = "اسم العميل";
                dataTable.Columns["email"].ColumnName = "البريد الإلكتروني";
                dataTable.Columns["phone"].ColumnName = "الهاتف";
                dataTable.Columns["address"].ColumnName = "العنوان";

                grid.DataSource = dataTable;

                // إخفاء عمود المعرف
                if (grid.Columns["المعرف"] != null)
                    grid.Columns["المعرف"].Visible = false;
            }
        }

        private void AddProduct()
        {
            ProductForm productForm = new ProductForm(connection);
            if (productForm.ShowDialog() == DialogResult.OK)
            {
                ShowProducts(); // تحديث القائمة
            }
        }

        private void EditProduct(int productId)
        {
            ProductForm productForm = new ProductForm(connection, productId);
            if (productForm.ShowDialog() == DialogResult.OK)
            {
                ShowProducts(); // تحديث القائمة
            }
        }

        private void AddCustomer()
        {
            CustomerForm customerForm = new CustomerForm(connection);
            if (customerForm.ShowDialog() == DialogResult.OK)
            {
                ShowCustomers(); // تحديث القائمة
            }
        }

        private void EditCustomer(int customerId)
        {
            CustomerForm customerForm = new CustomerForm(connection, customerId);
            if (customerForm.ShowDialog() == DialogResult.OK)
            {
                ShowCustomers(); // تحديث القائمة
            }
        }

        private void ShowInvoices()
        {
            MessageBox.Show("صفحة الفواتير قيد التطوير", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CreateInvoice()
        {
            MessageBox.Show("إنشاء الفواتير قيد التطوير", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SalesReport()
        {
            MessageBox.Show("تقرير المبيعات قيد التطوير", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void InventoryReport()
        {
            MessageBox.Show("تقرير المخزون قيد التطوير", "قريباً",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Logout()
        {
            currentUser = null;
            ShowLoginInterface();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            connection?.Close();
            base.OnFormClosing(e);
        }
    }
    
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
    }
}
