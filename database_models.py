#!/usr/bin/env python3
"""
نماذج قاعدة البيانات لنظام المحاسبة المتكامل
Database Models for Integrated Accounting System
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='employee')
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='created_by_user', lazy=True)
    expenses = db.relationship('Expense', backref='created_by_user', lazy=True)
    
    def set_password(self, password):
        """تعيين كلمة مرور مشفرة"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def permissions(self):
        """صلاحيات المستخدم حسب الدور"""
        permissions_map = {
            'admin': ['all'],
            'manager': ['invoices', 'expenses', 'reports', 'customers', 'products'],
            'accountant': ['invoices', 'expenses', 'reports'],
            'employee': ['expenses']
        }
        return permissions_map.get(self.role, ['expenses'])
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    customer_type = db.Column(db.String(20), default='individual')  # individual, company
    credit_limit = db.Column(db.Float, default=0.0)
    current_balance = db.Column(db.Float, default=0.0)
    discount_rate = db.Column(db.Float, default=0.0)
    loyalty_points = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'tax_number': self.tax_number,
            'customer_type': self.customer_type,
            'credit_limit': self.credit_limit,
            'current_balance': self.current_balance,
            'discount_rate': self.discount_rate,
            'loyalty_points': self.loyalty_points,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'notes': self.notes
        }

class Category(db.Model):
    """نموذج فئات المنتجات"""
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    parent = db.relationship('Category', remote_side=[id], backref='children')
    products = db.relationship('Product', backref='category', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    payment_terms = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    # العلاقات
    products = db.relationship('Product', backref='supplier', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'contact_person': self.contact_person,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'tax_number': self.tax_number,
            'payment_terms': self.payment_terms,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'notes': self.notes
        }

class Product(db.Model):
    """نموذج المنتجات"""
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    sku = db.Column(db.String(50), unique=True)  # رمز المنتج
    barcode = db.Column(db.String(50), unique=True)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    
    # الأسعار
    cost_price = db.Column(db.Float, nullable=False, default=0.0)  # سعر التكلفة
    selling_price = db.Column(db.Float, nullable=False, default=0.0)  # سعر البيع
    wholesale_price = db.Column(db.Float, default=0.0)  # سعر الجملة
    
    # المخزون
    current_stock = db.Column(db.Integer, default=0)
    min_stock_level = db.Column(db.Integer, default=0)  # الحد الأدنى للمخزون
    max_stock_level = db.Column(db.Integer, default=0)  # الحد الأقصى للمخزون
    
    # معلومات إضافية
    unit = db.Column(db.String(20), default='قطعة')  # وحدة القياس
    weight = db.Column(db.Float)
    expiry_date = db.Column(db.Date)
    is_active = db.Column(db.Boolean, default=True)
    is_taxable = db.Column(db.Boolean, default=True)
    tax_rate = db.Column(db.Float, default=15.0)  # ضريبة القيمة المضافة
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    invoice_items = db.relationship('InvoiceItem', backref='product', lazy=True)
    stock_movements = db.relationship('StockMovement', backref='product', lazy=True)
    
    @property
    def profit_margin(self):
        """هامش الربح"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0
    
    @property
    def is_low_stock(self):
        """هل المخزون منخفض"""
        return self.current_stock <= self.min_stock_level
    
    @property
    def stock_status(self):
        """حالة المخزون"""
        if self.current_stock == 0:
            return 'نفد'
        elif self.is_low_stock:
            return 'منخفض'
        else:
            return 'متوفر'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'sku': self.sku,
            'barcode': self.barcode,
            'category_id': self.category_id,
            'supplier_id': self.supplier_id,
            'cost_price': self.cost_price,
            'selling_price': self.selling_price,
            'wholesale_price': self.wholesale_price,
            'current_stock': self.current_stock,
            'min_stock_level': self.min_stock_level,
            'max_stock_level': self.max_stock_level,
            'unit': self.unit,
            'weight': self.weight,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'is_active': self.is_active,
            'is_taxable': self.is_taxable,
            'tax_rate': self.tax_rate,
            'profit_margin': self.profit_margin,
            'is_low_stock': self.is_low_stock,
            'stock_status': self.stock_status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Invoice(db.Model):
    """نموذج الفواتير"""
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # التواريخ
    issue_date = db.Column(db.Date, default=date.today)
    due_date = db.Column(db.Date)
    
    # المبالغ
    subtotal = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, default=0.0)
    paid_amount = db.Column(db.Float, default=0.0)
    
    # الحالة
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, overdue, cancelled
    payment_method = db.Column(db.String(20))  # cash, card, transfer, credit
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')
    
    @property
    def balance_due(self):
        """المبلغ المستحق"""
        return self.total_amount - self.paid_amount
    
    @property
    def is_paid(self):
        """هل الفاتورة مدفوعة"""
        return self.paid_amount >= self.total_amount
    
    @property
    def status_text(self):
        """نص الحالة بالعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        }
        return status_map.get(self.status, self.status)
    
    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        self.subtotal = sum(item.total for item in self.items)
        self.tax_amount = sum(item.tax_amount for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
    
    def to_dict(self):
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'customer_id': self.customer_id,
            'created_by': self.created_by,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': self.subtotal,
            'discount_amount': self.discount_amount,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'balance_due': self.balance_due,
            'status': self.status,
            'status_text': self.status_text,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'terms_conditions': self.terms_conditions,
            'is_paid': self.is_paid,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class InvoiceItem(db.Model):
    """نموذج بنود الفاتورة"""
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    quantity = db.Column(db.Float, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    discount_rate = db.Column(db.Float, default=0.0)
    tax_rate = db.Column(db.Float, default=15.0)

    @property
    def subtotal(self):
        """المجموع الفرعي قبل الخصم"""
        return self.quantity * self.unit_price

    @property
    def discount_amount(self):
        """مبلغ الخصم"""
        return self.subtotal * (self.discount_rate / 100)

    @property
    def net_amount(self):
        """المبلغ بعد الخصم"""
        return self.subtotal - self.discount_amount

    @property
    def tax_amount(self):
        """مبلغ الضريبة"""
        return self.net_amount * (self.tax_rate / 100)

    @property
    def total(self):
        """الإجمالي النهائي"""
        return self.net_amount + self.tax_amount

    def to_dict(self):
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'product_id': self.product_id,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'discount_rate': self.discount_rate,
            'tax_rate': self.tax_rate,
            'subtotal': self.subtotal,
            'discount_amount': self.discount_amount,
            'net_amount': self.net_amount,
            'tax_amount': self.tax_amount,
            'total': self.total
        }
