{% extends "demo_base.html" %}

{% block title %}المصروفات - نظام المحاسبة المتكامل{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="text-white fw-bold">
            <i class="fas fa-receipt me-2"></i>
            إدارة المصروفات
        </h2>
        <p class="text-white-50">تتبع وإدارة جميع مصروفات الشركة بطريقة منظمة</p>
    </div>
    <div class="col-md-4 text-end">
        <button class="btn btn-success btn-lg" onclick="showDemo('create')">
            <i class="fas fa-plus me-2"></i>
            مصروف جديد
        </button>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">15</div>
            <div class="text-muted">مصروفات مدفوعة</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">5</div>
            <div class="text-muted">في انتظار الموافقة</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stats-number">85,000</div>
            <div class="text-muted">إجمالي المصروفات (ر.س)</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon danger">
                <i class="fas fa-chart-pie"></i>
            </div>
            <div class="stats-number">8</div>
            <div class="text-muted">فئات المصروفات</div>
        </div>
    </div>
</div>

<!-- Expense Categories Chart -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    المصروفات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                <canvas id="expenseCategoryChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    أهم الفئات
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>الإيجار</span>
                    <span class="badge bg-primary">35%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>الرواتب</span>
                    <span class="badge bg-success">30%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>المرافق</span>
                    <span class="badge bg-warning">15%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>التسويق</span>
                    <span class="badge bg-info">12%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>أخرى</span>
                    <span class="badge bg-secondary">8%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expenses Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة المصروفات</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-filter me-1"></i>فلترة
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>رقم المرجع</th>
                                <th>العنوان</th>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td>
                                    <strong>{{ expense.reference_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ expense.title }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ expense.category }}</span>
                                </td>
                                <td>
                                    <strong>{{ "{:,.0f}".format(expense.amount) }} ر.س</strong>
                                </td>
                                <td>{{ expense.expense_date }}</td>
                                <td>
                                    <span class="badge bg-{{ expense.status_color }}">
                                        {{ expense.status_text }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="showDemo('view')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if expense.status == 'pending' %}
                                        <button class="btn btn-outline-success" onclick="showDemo('approve')" title="موافقة">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="showDemo('reject')" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        {% if expense.status == 'approved' %}
                                        <button class="btn btn-outline-info" onclick="showDemo('pay')" title="دفع">
                                            <i class="fas fa-money-bill"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم دفع مصروف الإيجار</h6>
                            <p class="text-muted mb-1">مبلغ 12,000 ر.س - إيجار المكتب لشهر ديسمبر</p>
                            <small class="text-muted">منذ ساعتين</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">مصروف جديد في انتظار الموافقة</h6>
                            <p class="text-muted mb-1">مبلغ 450 ر.س - مستلزمات مكتبية</p>
                            <small class="text-muted">منذ 4 ساعات</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم الموافقة على مصروف الكهرباء</h6>
                            <p class="text-muted mb-1">مبلغ 850 ر.س - فاتورة الكهرباء</p>
                            <small class="text-muted">منذ يوم واحد</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Expense Category Chart
    const ctx = document.getElementById('expenseCategoryChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['الإيجار', 'الرواتب', 'المرافق', 'التسويق', 'المكتبية', 'الصيانة', 'أخرى'],
            datasets: [{
                label: 'المبلغ (ر.س)',
                data: [12000, 25000, 3500, 2500, 450, 1200, 800],
                backgroundColor: [
                    '#2563eb',
                    '#059669',
                    '#d97706',
                    '#dc2626',
                    '#7c3aed',
                    '#0891b2',
                    '#64748b'
                ],
                borderRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });
});

function showDemo(action) {
    const messages = {
        'create': 'في النسخة الكاملة، ستتمكن من إضافة مصروفات جديدة مع تصنيفها وإرفاق الإيصالات!',
        'view': 'في النسخة الكاملة، ستتمكن من عرض تفاصيل المصروف والإيصالات المرفقة!',
        'approve': 'في النسخة الكاملة، ستتمكن من الموافقة على المصروفات وإضافة ملاحظات!',
        'reject': 'في النسخة الكاملة، ستتمكن من رفض المصروفات مع تحديد السبب!',
        'pay': 'في النسخة الكاملة، ستتمكن من تسجيل دفع المصروفات وربطها بالحسابات!'
    };
    
    alert(messages[action] || 'هذه ميزة متاحة في النسخة الكاملة من النظام!');
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #e2e8f0;
}

.timeline-content {
    background: #f8fafc;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #e2e8f0;
}
</style>
{% endblock %}
