#!/usr/bin/env python3
"""
نظام المحاسبة المتكامل - الإصدار الثاني مع قاعدة البيانات
Integrated Accounting System - Version 2 with Database
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from database_models import db, User, Customer, Category, Supplier, Product, Invoice, InvoiceItem, Expense, Employee, Payroll
from datetime import datetime, date, timedelta
import os

def create_app():
    """إنشاء تطبيق Flask"""
    
    app = Flask(__name__)
    
    # إعدادات التطبيق
    basedir = os.path.abspath(os.path.dirname(__file__))
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(basedir, "accounting_system.db")}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'accounting-system-secret-key-2024'
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    return app

app = create_app()

# قالب القاعدة الأساسية (نفس القالب السابق)
BASE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المحاسبة المتكامل{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
        }
        .navbar { background: rgba(5, 150, 105, 0.95) !important; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        .btn-primary { background: linear-gradient(135deg, #10b981, #059669); border: none; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4); }
        .alert { border: none; border-radius: 12px; }
        .alert-success { background: rgba(16, 185, 129, 0.1); color: #059669; border-right: 4px solid #10b981; }
        .alert-danger { background: rgba(239, 68, 68, 0.1); color: #dc2626; border-right: 4px solid #ef4444; }
        .alert-info { background: rgba(59, 130, 246, 0.1); color: #2563eb; border-right: 4px solid #3b82f6; }
        .table { background: white; border-radius: 15px; overflow: hidden; }
        .table thead th { background: linear-gradient(135deg, #f8fafc, #e2e8f0); border: none; font-weight: 600; color: #1e293b; }
        .badge { font-size: 0.75rem; padding: 0.5rem 1rem; border-radius: 50px; }
    </style>
</head>
<body>
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-store me-1"></i>إدارة المتجر
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/products"><i class="fas fa-box me-2"></i>المنتجات</a></li>
                            <li><a class="dropdown-item" href="/categories"><i class="fas fa-tags me-2"></i>الفئات</a></li>
                            <li><a class="dropdown-item" href="/suppliers"><i class="fas fa-truck me-2"></i>الموردين</a></li>
                            <li><a class="dropdown-item" href="/customers"><i class="fas fa-users me-2"></i>العملاء</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/invoices">
                            <i class="fas fa-file-invoice me-1"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/expenses">
                            <i class="fas fa-receipt me-1"></i>المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/payroll">
                            <i class="fas fa-users me-1"></i>الرواتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ session.user_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <main class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
'''

# قالب تسجيل الدخول (نفس القالب السابق مع تحديثات بسيطة)
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .login-logo i {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
        .demo-info {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .demo-credentials {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .credential-item:last-child { border-bottom: none; }
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border-right: 4px solid #ef4444;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border-right: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-logo text-center">
            <i class="fas fa-calculator"></i>
            <h2 class="fw-bold" style="color: #10b981;">نظام المحاسبة المتكامل</h2>
            <p class="text-muted">الإصدار الثاني مع قاعدة البيانات</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="demo-info">
            <i class="fas fa-database me-2"></i>
            <strong>نظام متطور</strong> - مع قاعدة بيانات حقيقية وميزات متقدمة
        </div>

        <form method="POST" id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label fw-bold">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>

        <div class="demo-credentials">
            <h6 style="color: #059669;"><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول:</h6>
            
            <div class="credential-item">
                <span><strong>المدير:</strong></span>
                <div>
                    <code>admin</code> / <code>admin123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('admin', 'admin123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>المحاسب:</strong></span>
                <div>
                    <code>accountant</code> / <code>acc123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('accountant', 'acc123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>المدير:</strong></span>
                <div>
                    <code>manager</code> / <code>mgr123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('manager', 'mgr123')">
                        استخدام
                    </button>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <small class="text-muted">
                &copy; 2024 نظام المحاسبة المتكامل - الإصدار الثاني
            </small>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            usernameField.style.background = '#dcfce7';
            passwordField.style.background = '#dcfce7';
            
            setTimeout(() => {
                usernameField.style.background = '';
                passwordField.style.background = '';
            }, 1000);
        }

        // تأثير التحميل عند الإرسال
        document.getElementById('loginForm').addEventListener('submit', function() {
            const btn = document.getElementById('loginBtn');
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري تسجيل الدخول...';
            btn.disabled = true;
        });

        document.getElementById('username').focus();
    </script>
</body>
</html>
'''

# المسارات الأساسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # البحث عن المستخدم في قاعدة البيانات
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            # تسجيل الدخول بنجاح
            session['user_id'] = user.id
            session['username'] = user.username
            session['user_name'] = user.full_name
            session['user_role'] = user.role
            session['user_permissions'] = user.permissions

            # تحديث آخر تسجيل دخول
            user.last_login = datetime.utcnow()
            db.session.commit()

            flash(f'مرحباً {user.full_name}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

if __name__ == '__main__':
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('accounting_system.db'):
        print("❌ قاعدة البيانات غير موجودة!")
        print("💡 قم بتشغيل: python database_setup.py")
        exit(1)

    print("🚀 تم تشغيل نظام المحاسبة المتكامل - الإصدار الثاني!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   المدير: manager / mgr123")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
