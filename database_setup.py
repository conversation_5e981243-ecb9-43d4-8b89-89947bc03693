#!/usr/bin/env python3
"""
إعداد قاعدة البيانات وإدراج البيانات التجريبية
Database Setup and Sample Data Insertion
"""

from flask import Flask
from database_models import db, User, Customer, Category, Supplier, Product, Invoice, InvoiceItem, Expense, Employee, Payroll
from datetime import datetime, date, timedelta
import os

def create_app():
    """إنشاء تطبيق Flask مع قاعدة البيانات"""
    
    app = Flask(__name__)
    
    # إعدادات قاعدة البيانات
    basedir = os.path.abspath(os.path.dirname(__file__))
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(basedir, "accounting_system.db")}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'accounting-system-secret-key-2024'
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    return app

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    
    users_data = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'أحمد',
            'last_name': 'المدير',
            'role': 'admin'
        },
        {
            'username': 'accountant',
            'email': '<EMAIL>',
            'password': 'acc123',
            'first_name': 'فاطمة',
            'last_name': 'المحاسبة',
            'role': 'accountant'
        },
        {
            'username': 'employee',
            'email': '<EMAIL>',
            'password': 'emp123',
            'first_name': 'محمد',
            'last_name': 'الموظف',
            'role': 'employee'
        },
        {
            'username': 'manager',
            'email': '<EMAIL>',
            'password': 'mgr123',
            'first_name': 'سارة',
            'last_name': 'المديرة',
            'role': 'manager'
        }
    ]
    
    for user_data in users_data:
        # التحقق من عدم وجود المستخدم
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if not existing_user:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])
            db.session.add(user)
    
    db.session.commit()
    print("✅ تم إنشاء المستخدمين التجريبيين")

def create_sample_customers():
    """إنشاء عملاء تجريبيين"""
    
    customers_data = [
        {
            'name': 'شركة التقنية المتقدمة',
            'email': '<EMAIL>',
            'phone': '0112345678',
            'address': 'الرياض، المملكة العربية السعودية',
            'tax_number': '300123456789003',
            'customer_type': 'company',
            'credit_limit': 50000.0,
            'discount_rate': 5.0
        },
        {
            'name': 'مؤسسة الحلول الذكية',
            'email': '<EMAIL>',
            'phone': '0113456789',
            'address': 'جدة، المملكة العربية السعودية',
            'tax_number': '300234567890003',
            'customer_type': 'company',
            'credit_limit': 30000.0,
            'discount_rate': 3.0
        },
        {
            'name': 'أحمد محمد العلي',
            'email': '<EMAIL>',
            'phone': '0501234567',
            'address': 'الدمام، المملكة العربية السعودية',
            'customer_type': 'individual',
            'credit_limit': 5000.0,
            'loyalty_points': 150
        },
        {
            'name': 'شركة الابتكار التقني',
            'email': '<EMAIL>',
            'phone': '0114567890',
            'address': 'الخبر، المملكة العربية السعودية',
            'tax_number': '300345678901003',
            'customer_type': 'company',
            'credit_limit': 75000.0,
            'discount_rate': 7.0
        }
    ]
    
    for customer_data in customers_data:
        existing_customer = Customer.query.filter_by(name=customer_data['name']).first()
        if not existing_customer:
            customer = Customer(**customer_data)
            db.session.add(customer)
    
    db.session.commit()
    print("✅ تم إنشاء العملاء التجريبيين")

def create_sample_categories():
    """إنشاء فئات المنتجات"""
    
    categories_data = [
        {'name': 'الأطعمة والمشروبات', 'description': 'جميع المواد الغذائية والمشروبات'},
        {'name': 'منتجات الألبان', 'description': 'الحليب والجبن والزبدة'},
        {'name': 'اللحوم والدواجن', 'description': 'اللحوم الطازجة والمجمدة'},
        {'name': 'الخضروات والفواكه', 'description': 'الخضروات والفواكه الطازجة'},
        {'name': 'المخبوزات', 'description': 'الخبز والمعجنات'},
        {'name': 'المنظفات', 'description': 'منتجات التنظيف والعناية'},
        {'name': 'العناية الشخصية', 'description': 'منتجات العناية والتجميل'},
        {'name': 'الأدوات المنزلية', 'description': 'الأدوات والمستلزمات المنزلية'}
    ]
    
    for category_data in categories_data:
        existing_category = Category.query.filter_by(name=category_data['name']).first()
        if not existing_category:
            category = Category(**category_data)
            db.session.add(category)
    
    db.session.commit()
    print("✅ تم إنشاء فئات المنتجات")

def create_sample_suppliers():
    """إنشاء موردين تجريبيين"""
    
    suppliers_data = [
        {
            'name': 'شركة المواد الغذائية المتحدة',
            'contact_person': 'خالد أحمد',
            'email': '<EMAIL>',
            'phone': '0112223333',
            'address': 'الرياض، المملكة العربية السعودية',
            'tax_number': '300111222333003',
            'payment_terms': 'دفع خلال 30 يوم'
        },
        {
            'name': 'مؤسسة الألبان الطازجة',
            'contact_person': 'فاطمة محمد',
            'email': '<EMAIL>',
            'phone': '0113334444',
            'address': 'جدة، المملكة العربية السعودية',
            'tax_number': '300222333444003',
            'payment_terms': 'دفع فوري'
        },
        {
            'name': 'شركة المنظفات الحديثة',
            'contact_person': 'عبدالله سالم',
            'email': '<EMAIL>',
            'phone': '0114445555',
            'address': 'الدمام، المملكة العربية السعودية',
            'tax_number': '300333444555003',
            'payment_terms': 'دفع خلال 15 يوم'
        }
    ]
    
    for supplier_data in suppliers_data:
        existing_supplier = Supplier.query.filter_by(name=supplier_data['name']).first()
        if not existing_supplier:
            supplier = Supplier(**supplier_data)
            db.session.add(supplier)
    
    db.session.commit()
    print("✅ تم إنشاء الموردين التجريبيين")

def create_sample_products():
    """إنشاء منتجات تجريبية"""
    
    products_data = [
        {
            'name': 'حليب طازج - 1 لتر',
            'description': 'حليب طازج كامل الدسم',
            'sku': 'MILK001',
            'barcode': '6281234567890',
            'category_id': 2,  # منتجات الألبان
            'supplier_id': 2,  # مؤسسة الألبان الطازجة
            'cost_price': 3.50,
            'selling_price': 5.00,
            'current_stock': 150,
            'min_stock_level': 20,
            'max_stock_level': 300,
            'unit': 'لتر'
        },
        {
            'name': 'خبز أبيض طازج',
            'description': 'خبز أبيض طازج يومياً',
            'sku': 'BREAD001',
            'barcode': '6281234567891',
            'category_id': 5,  # المخبوزات
            'supplier_id': 1,  # شركة المواد الغذائية المتحدة
            'cost_price': 1.00,
            'selling_price': 1.50,
            'current_stock': 80,
            'min_stock_level': 10,
            'max_stock_level': 200,
            'unit': 'رغيف'
        },
        {
            'name': 'دجاج طازج - كيلو',
            'description': 'دجاج طازج محلي',
            'sku': 'CHICKEN001',
            'barcode': '6281234567892',
            'category_id': 3,  # اللحوم والدواجن
            'supplier_id': 1,
            'cost_price': 18.00,
            'selling_price': 25.00,
            'current_stock': 45,
            'min_stock_level': 5,
            'max_stock_level': 100,
            'unit': 'كيلو'
        },
        {
            'name': 'تفاح أحمر - كيلو',
            'description': 'تفاح أحمر طازج مستورد',
            'sku': 'APPLE001',
            'barcode': '6281234567893',
            'category_id': 4,  # الخضروات والفواكه
            'supplier_id': 1,
            'cost_price': 8.00,
            'selling_price': 12.00,
            'current_stock': 25,
            'min_stock_level': 10,
            'max_stock_level': 80,
            'unit': 'كيلو'
        },
        {
            'name': 'منظف أطباق - 500 مل',
            'description': 'منظف أطباق فعال ضد الدهون',
            'sku': 'DETERGENT001',
            'barcode': '6281234567894',
            'category_id': 6,  # المنظفات
            'supplier_id': 3,  # شركة المنظفات الحديثة
            'cost_price': 4.50,
            'selling_price': 7.00,
            'current_stock': 60,
            'min_stock_level': 15,
            'max_stock_level': 120,
            'unit': 'زجاجة'
        }
    ]
    
    for product_data in products_data:
        existing_product = Product.query.filter_by(sku=product_data['sku']).first()
        if not existing_product:
            product = Product(**product_data)
            db.session.add(product)
    
    db.session.commit()
    print("✅ تم إنشاء المنتجات التجريبية")

def create_sample_employees():
    """إنشاء موظفين تجريبيين"""
    
    employees_data = [
        {
            'employee_id': 'EMP001',
            'first_name': 'أحمد',
            'last_name': 'محمد علي',
            'email': '<EMAIL>',
            'phone': '0501111111',
            'position': 'مطور أول',
            'department': 'تقنية المعلومات',
            'hire_date': date(2023, 1, 15),
            'basic_salary': 8000.0,
            'housing_allowance': 1000.0,
            'transport_allowance': 500.0
        },
        {
            'employee_id': 'EMP002',
            'first_name': 'فاطمة',
            'last_name': 'أحمد السالم',
            'email': '<EMAIL>',
            'phone': '0502222222',
            'position': 'محاسبة رئيسية',
            'department': 'المالية',
            'hire_date': date(2022, 6, 1),
            'basic_salary': 7000.0,
            'housing_allowance': 800.0,
            'transport_allowance': 400.0
        },
        {
            'employee_id': 'EMP003',
            'first_name': 'محمد',
            'last_name': 'عبدالله الزهراني',
            'email': '<EMAIL>',
            'phone': '0503333333',
            'position': 'مدير المبيعات',
            'department': 'المبيعات',
            'hire_date': date(2021, 3, 10),
            'basic_salary': 9000.0,
            'housing_allowance': 1200.0,
            'transport_allowance': 800.0
        }
    ]
    
    for employee_data in employees_data:
        existing_employee = Employee.query.filter_by(employee_id=employee_data['employee_id']).first()
        if not existing_employee:
            employee = Employee(**employee_data)
            db.session.add(employee)
    
    db.session.commit()
    print("✅ تم إنشاء الموظفين التجريبيين")

def setup_database():
    """إعداد قاعدة البيانات الكاملة"""
    
    print("🚀 بدء إعداد قاعدة البيانات...")
    
    app = create_app()
    
    with app.app_context():
        # إنشاء الجداول
        print("📊 إنشاء جداول قاعدة البيانات...")
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # إدراج البيانات التجريبية
        create_sample_users()
        create_sample_categories()
        create_sample_suppliers()
        create_sample_customers()
        create_sample_products()
        create_sample_employees()
        
        print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("📁 ملف قاعدة البيانات: accounting_system.db")
        print("\n🔑 بيانات تسجيل الدخول:")
        print("   المدير: admin / admin123")
        print("   المحاسب: accountant / acc123")
        print("   الموظف: employee / emp123")
        print("   المدير: manager / mgr123")
    
    return app

if __name__ == '__main__':
    setup_database()
