#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء نظام المحاسبة كملف تنفيذي
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    print("🏗️  بناء نظام المحاسبة التنفيذي")
    print("=" * 50)
    
    # التحقق من وجود الملفات المطلوبة
    if not Path('accounting_system_db.py').exists():
        print("❌ خطأ: لم يتم العثور على ملف النظام الرئيسي")
        return
    
    # إنشاء ملف .spec
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['accounting_system_db.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('accounting_system.db', '.'),
        ('remote_access_guide.md', '.'),
    ],
    hiddenimports=[
        'flask',
        'sqlite3',
        'datetime',
        'hashlib',
        'secrets',
        'json',
        'zipfile',
        'shutil',
        'psutil',
        'platform'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_المحاسبة',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('accounting_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف .spec")
    
    # إنشاء ملف بدء التشغيل
    startup_content = '''@echo off
title نظام المحاسبة - جاري التشغيل...
echo.
echo ================================================
echo           نظام المحاسبة المتكامل
echo ================================================
echo.
echo جاري تشغيل النظام...
echo انتظر حتى يظهر "Running on http://127.0.0.1:5000"
echo ثم افتح المتصفح واذهب إلى: http://localhost:5000
echo.

REM تشغيل النظام
"نظام_المحاسبة.exe"

REM في حالة إغلاق النظام
echo.
echo تم إغلاق النظام
echo اضغط أي مفتاح للخروج...
pause > nul
'''
    
    with open('تشغيل_النظام.bat', 'w', encoding='utf-8') as f:
        f.write(startup_content)
    
    print("✅ تم إنشاء ملف بدء التشغيل")
    
    # إنشاء ملف التعليمات
    readme_content = '''# نظام المحاسبة المتكامل

## 🚀 كيفية التشغيل

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف "تشغيل_النظام.bat"
2. انتظر حتى يظهر "Running on http://127.0.0.1:5000"
3. افتح المتصفح واذهب إلى: http://localhost:5000

### الطريقة الثانية:
1. انقر نقراً مزدوجاً على ملف "نظام_المحاسبة.exe"
2. افتح المتصفح واذهب إلى: http://localhost:5000

## 🔑 بيانات تسجيل الدخول

### المدير العام:
- اسم المستخدم: admin
- كلمة المرور: admin123

### المحاسب:
- اسم المستخدم: accountant
- كلمة المرور: acc123

### مدير المتجر:
- اسم المستخدم: manager
- كلمة المرور: mgr123

## 📁 الملفات المهمة

- نظام_المحاسبة.exe - الملف التنفيذي الرئيسي
- accounting_system.db - قاعدة البيانات
- تشغيل_النظام.bat - ملف بدء التشغيل السهل
- اقرأني.txt - هذا الملف

## ⚠️ تحذيرات مهمة

1. لا تحذف ملف قاعدة البيانات (accounting_system.db)
2. قم بنسخ احتياطية دورية من قاعدة البيانات
3. تأكد من إغلاق النظام بشكل صحيح قبل إيقاف تشغيل الكمبيوتر
4. لا تشغل أكثر من نسخة واحدة من النظام في نفس الوقت

## 🔧 حل المشاكل الشائعة

### المشكلة: النظام لا يفتح في المتصفح
الحل: تأكد من أن المنفذ 5000 غير مستخدم من برنامج آخر

### المشكلة: رسالة خطأ عند التشغيل
الحل: تشغيل النظام كمدير (Run as Administrator)

### المشكلة: فقدان البيانات
الحل: استعادة النسخة الاحتياطية من قاعدة البيانات

---
تم تطوير هذا النظام بواسطة فريق التطوير المتخصص
'''
    
    with open('اقرأني.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف التعليمات")
    
    # بناء الملف التنفيذي
    print("🔨 جاري بناء الملف التنفيذي...")
    print("هذا قد يستغرق عدة دقائق...")
    
    try:
        result = subprocess.run([
            'py', '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'accounting_system.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            
            # نسخ الملفات المهمة إلى مجلد dist
            dist_path = Path('dist')
            if dist_path.exists():
                files_to_copy = [
                    'accounting_system.db',
                    'تشغيل_النظام.bat',
                    'اقرأني.txt',
                    'remote_access_guide.md'
                ]
                
                for file_name in files_to_copy:
                    if Path(file_name).exists():
                        shutil.copy2(file_name, dist_path)
                        print(f"📄 تم نسخ {file_name}")
                
                print(f"\n📁 الملفات جاهزة في مجلد: {dist_path.absolute()}")
                print("🎉 يمكنك الآن توزيع محتويات مجلد dist")
                print("\n📋 محتويات المجلد:")
                for item in dist_path.iterdir():
                    print(f"  - {item.name}")
            
        else:
            print("❌ حدث خطأ أثناء البناء:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    print("\n🎯 تم الانتهاء من عملية البناء!")

if __name__ == "__main__":
    main()
