45#!/usr/bin/env python3
"""
نظام المحاسبة المتكامل مع قاعدة البيانات
Integrated Accounting System with Database
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify, send_file
import sqlite3
import os
from datetime import datetime, date

app = Flask(__name__)
app.secret_key = 'accounting-system-db-2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect('accounting_system.db')
    conn.row_factory = sqlite3.Row
    return conn

def log_audit(action, table_name, record_id=None, old_values=None, new_values=None):
    """تسجيل عملية في سجل التدقيق"""
    try:
        if 'user_id' not in session:
            return

        conn = get_db_connection()

        # الحصول على عنوان IP (محاكاة)
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))

        # تحويل القيم إلى JSON للتخزين
        import json
        old_values_json = json.dumps(old_values, ensure_ascii=False) if old_values else None
        new_values_json = json.dumps(new_values, ensure_ascii=False) if new_values else None

        conn.execute('''
            INSERT INTO audit_log (user_id, user_name, action, table_name, record_id, old_values, new_values, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session['user_id'],
            session.get('username', 'unknown'),
            action,
            table_name,
            record_id,
            old_values_json,
            new_values_json,
            ip_address
        ))

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"خطأ في تسجيل التدقيق: {e}")

def generate_remote_token():
    """إنشاء رمز وصول عن بُعد آمن"""
    import secrets
    import string
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))

def log_remote_connection(client_id, operation, status, details=None):
    """تسجيل محاولة اتصال عن بُعد"""
    try:
        conn = get_db_connection()
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')

        conn.execute('''
            INSERT INTO remote_connections (client_id, operation, ip_address, user_agent, status, details)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (client_id, operation, ip_address, user_agent, status, details))

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"خطأ في تسجيل الاتصال عن بُعد: {e}")

def verify_remote_access(token):
    """التحقق من صحة رمز الوصول عن بُعد"""
    try:
        conn = get_db_connection()
        access = conn.execute('''
            SELECT * FROM remote_access
            WHERE access_token = ? AND is_active = 1 AND expires_at > datetime('now')
        ''', (token,)).fetchone()

        if access:
            # تحديث آخر اتصال وعدد الاتصالات
            conn.execute('''
                UPDATE remote_access
                SET last_connection = datetime('now'), connection_count = connection_count + 1
                WHERE access_token = ?
            ''', (token,))
            conn.commit()

        conn.close()
        return dict(access) if access else None
    except Exception as e:
        print(f"خطأ في التحقق من الوصول عن بُعد: {e}")
        return None

def check_password(stored_password, provided_password):
    """التحقق من كلمة المرور (مبسط)"""
    return stored_password == provided_password

# قالب القاعدة الأساسية
BASE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المحاسبة المتكامل{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #F5F5F5 0%, #F0EAD6 100%);
            min-height: 100vh;
        }
        .navbar { background: #0A4F6E !important; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2E86AB, #0A4F6E);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, #2E86AB, #0A4F6E);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333333;
            margin-bottom: 0.5rem;
        }
        .btn-primary { background: linear-gradient(135deg, #2E86AB, #0A4F6E); border: none; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(46, 134, 171, 0.4); }
        .btn-success { background: linear-gradient(135deg, #3A7D44, #1E5631); border: none; }
        .btn-success:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(58, 125, 68, 0.4); }
        .btn-warning { background: linear-gradient(135deg, #F39237, #E67E22); border: none; color: white; }
        .btn-warning:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(243, 146, 55, 0.4); color: white; }
        .alert { border: none; border-radius: 12px; }
        .alert-success { background: rgba(58, 125, 68, 0.1); color: #1E5631; border-right: 4px solid #3A7D44; }
        .alert-danger { background: rgba(231, 76, 60, 0.1); color: #E74C3C; border-right: 4px solid #E74C3C; }
        .alert-info { background: rgba(46, 134, 171, 0.1); color: #0A4F6E; border-right: 4px solid #2E86AB; }
        .alert-warning { background: rgba(243, 146, 55, 0.1); color: #E67E22; border-right: 4px solid #F39237; }
        .table { background: white; border-radius: 15px; overflow: hidden; }
        .table thead th { background: linear-gradient(135deg, #F5F5F5, #e2e8f0); border: none; font-weight: 600; color: #333333; }
        .badge { font-size: 0.75rem; padding: 0.5rem 1rem; border-radius: 50px; }
        .badge.bg-success { background: #3A7D44 !important; }
        .badge.bg-info { background: #2E86AB !important; }
        .badge.bg-warning { background: #F39237 !important; color: white !important; }
        .badge.bg-danger { background: #E74C3C !important; }
    </style>
</head>
<body>
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-store me-1"></i>إدارة المتجر
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/products"><i class="fas fa-box me-2"></i>المنتجات</a></li>
                            <li><a class="dropdown-item" href="/categories"><i class="fas fa-tags me-2"></i>الفئات</a></li>
                            <li><a class="dropdown-item" href="/suppliers"><i class="fas fa-truck me-2"></i>الموردين</a></li>
                            <li><a class="dropdown-item" href="/customers"><i class="fas fa-users me-2"></i>العملاء</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/invoices">
                            <i class="fas fa-file-invoice me-1"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/expenses">
                            <i class="fas fa-receipt me-1"></i>المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/payroll">
                            <i class="fas fa-users me-1"></i>الرواتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cogs me-1"></i>أنظمة متقدمة
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/e-invoice"><i class="fas fa-file-invoice-dollar me-2"></i>الفاتورة الإلكترونية</a></li>
                            <li><a class="dropdown-item" href="/branches"><i class="fas fa-building me-2"></i>إدارة الفروع</a></li>
                            <li><a class="dropdown-item" href="/notifications"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            {% if session.user_role == 'admin' %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/audit-log"><i class="fas fa-shield-alt me-2"></i>سجل التدقيق</a></li>
                            <li><a class="dropdown-item" href="/remote-access"><i class="fas fa-satellite-dish me-2"></i>الوصول عن بُعد</a></li>
                            {% endif %}
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ session.user_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><a class="dropdown-item" href="/notifications"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <main class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث عداد الإشعارات
        function updateNotificationBadge() {
            fetch('/api/notifications')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notificationBadge');
                    if (badge && data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline';
                    } else if (badge) {
                        badge.style.display = 'none';
                    }
                })
                .catch(error => console.log('خطأ في تحديث الإشعارات:', error));
        }

        // تحديث الإشعارات كل 30 ثانية
        if (document.getElementById('notificationBadge')) {
            updateNotificationBadge();
            setInterval(updateNotificationBadge, 30000);
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
'''

# قالب تسجيل الدخول
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body {
            background: linear-gradient(135deg, #2E86AB 0%, #0A4F6E 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .login-logo i {
            font-size: 4rem;
            color: #2E86AB;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #2E86AB;
            box-shadow: 0 0 0 3px rgba(46, 134, 171, 0.1);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #2E86AB, #0A4F6E);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 134, 171, 0.3);
        }
        .demo-info {
            background: linear-gradient(135deg, #3A7D44, #1E5631);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .demo-credentials {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .credential-item:last-child { border-bottom: none; }
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border-right: 4px solid #ef4444;
        }
        .alert-success {
            background: rgba(58, 125, 68, 0.1);
            color: #1E5631;
            border-right: 4px solid #3A7D44;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-logo text-center">
            <i class="fas fa-calculator"></i>
            <h2 class="fw-bold" style="color: #2E86AB;">نظام المحاسبة المتكامل</h2>
            <p class="text-muted">مع قاعدة بيانات حقيقية وميزات متقدمة</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="demo-info">
            <i class="fas fa-database me-2"></i>
            <strong>نظام متطور</strong> - مع قاعدة بيانات SQLite وإدارة شاملة
        </div>

        <form method="POST" id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label fw-bold">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>

        <div class="demo-credentials">
            <h6 style="color: #1E5631;"><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول:</h6>
            
            <div class="credential-item">
                <span><strong>المدير:</strong></span>
                <div>
                    <code>admin</code> / <code>admin123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('admin', 'admin123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>المحاسب:</strong></span>
                <div>
                    <code>accountant</code> / <code>acc123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('accountant', 'acc123')">
                        استخدام
                    </button>
                </div>
            </div>
            
            <div class="credential-item">
                <span><strong>مدير المتجر:</strong></span>
                <div>
                    <code>manager</code> / <code>mgr123</code>
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="fillCredentials('manager', 'mgr123')">
                        استخدام
                    </button>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <small class="text-muted">
                &copy; 2024 نظام المحاسبة المتكامل - مع قاعدة بيانات
            </small>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            usernameField.style.background = '#dcfce7';
            passwordField.style.background = '#dcfce7';
            
            setTimeout(() => {
                usernameField.style.background = '';
                passwordField.style.background = '';
            }, 1000);
        }

        // تأثير التحميل عند الإرسال
        document.getElementById('loginForm').addEventListener('submit', function() {
            const btn = document.getElementById('loginBtn');
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري تسجيل الدخول...';
            btn.disabled = true;
        });

        document.getElementById('username').focus();
    </script>
</body>
</html>
'''

# المسارات الأساسية
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # البحث عن المستخدم في قاعدة البيانات
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM users WHERE username = ? AND is_active = 1',
            (username,)
        ).fetchone()
        conn.close()

        if user and check_password(user['password_hash'], password):
            # تسجيل الدخول بنجاح
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['user_name'] = f"{user['first_name']} {user['last_name']}"
            session['user_role'] = user['role']

            # تحديث آخر تسجيل دخول
            conn = get_db_connection()
            conn.execute(
                'UPDATE users SET last_login = ? WHERE id = ?',
                (datetime.now(), user['id'])
            )
            conn.commit()
            conn.close()

            flash(f'مرحباً {session["user_name"]}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # حساب الإحصائيات من قاعدة البيانات
    stats = {}

    # إحصائيات العملاء
    stats['total_customers'] = conn.execute('SELECT COUNT(*) as count FROM customers WHERE is_active = 1').fetchone()['count']

    # إحصائيات المنتجات
    stats['total_products'] = conn.execute('SELECT COUNT(*) as count FROM products WHERE is_active = 1').fetchone()['count']
    stats['low_stock_products'] = conn.execute('SELECT COUNT(*) as count FROM products WHERE current_stock <= min_stock_level AND is_active = 1').fetchone()['count']

    # إحصائيات الفواتير
    stats['total_invoices'] = conn.execute('SELECT COUNT(*) as count FROM invoices').fetchone()['count']

    # إحصائيات مالية (مبسطة)
    stats['total_revenue'] = 75500  # مؤقت
    stats['total_expenses'] = 15800  # مؤقت
    stats['net_profit'] = stats['total_revenue'] - stats['total_expenses']

    # الأنشطة الحديثة
    recent_activities = [
        {
            'icon': 'fas fa-database',
            'title': 'تم تحديث قاعدة البيانات',
            'description': f'النظام يحتوي على {stats["total_products"]} منتج و {stats["total_customers"]} عميل',
            'time_ago': 'الآن'
        },
        {
            'icon': 'fas fa-box',
            'title': 'إدارة المنتجات متاحة',
            'description': 'يمكنك الآن إضافة وتعديل المنتجات',
            'time_ago': 'منذ دقائق'
        },
        {
            'icon': 'fas fa-users',
            'title': 'إدارة العملاء متاحة',
            'description': 'نظام شامل لإدارة بيانات العملاء',
            'time_ago': 'منذ دقائق'
        }
    ]

    conn.close()

    dashboard_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 bg-gradient text-white" style="background: linear-gradient(135deg, #2E86AB, #0A4F6E);">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="fw-bold mb-2">مرحباً {{ user_name }}</h2>
                                <p class="mb-0 opacity-75">النظام الآن يعمل مع قاعدة بيانات حقيقية</p>
                                <small class="opacity-75">
                                    <i class="fas fa-database me-1"></i>
                                    {{ total_products }} منتج • {{ total_customers }} عميل • {{ total_invoices }} فاتورة
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-database fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number">{{ total_customers }}</div>
                    <div class="text-muted">إجمالي العملاء</div>
                    <small class="text-success">
                        <i class="fas fa-check me-1"></i>عملاء نشطين
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stats-number">{{ total_products }}</div>
                    <div class="text-muted">إجمالي المنتجات</div>
                    {% if low_stock_products > 0 %}
                    <small class="text-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>{{ low_stock_products }} منتج منخفض المخزون
                    </small>
                    {% else %}
                    <small class="text-success">
                        <i class="fas fa-check me-1"></i>جميع المنتجات متوفرة
                    </small>
                    {% endif %}
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stats-number">{{ total_invoices }}</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                    <small class="text-info">
                        <i class="fas fa-info-circle me-1"></i>في قاعدة البيانات
                    </small>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(total_revenue) }}</div>
                    <div class="text-muted">إجمالي الإيرادات (ر.س)</div>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>من الفواتير المدفوعة
                    </small>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-xl-8 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-database me-2 text-primary"></i>
                            حالة قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-primary">{{ total_customers }}</h3>
                                    <p class="text-muted mb-0">عملاء مسجلين</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-success">{{ total_products }}</h3>
                                    <p class="text-muted mb-0">منتجات متاحة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-info">8</h3>
                                    <p class="text-muted mb-0">فئات منتجات</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3">
                                    <h3 class="text-warning">3</h3>
                                    <p class="text-muted mb-0">موردين</p>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>قاعدة البيانات جاهزة!</strong> يمكنك الآن إضافة وتعديل البيانات بشكل دائم.
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 mb-3">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">الميزات الجديدة</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            {% for activity in recent_activities %}
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="{{ activity.icon }}"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">{{ activity.title }}</h6>
                                        <p class="text-muted mb-1 small">{{ activity.description }}</p>
                                        <small class="text-muted">{{ activity.time_ago }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-rocket me-2 text-primary"></i>
                            الوحدات المتاحة الآن
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <a href="/products" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-box fa-2x mb-2 d-block"></i>
                                    إدارة المنتجات
                                    <small class="d-block text-muted">إضافة وتعديل المنتجات</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/invoices" class="btn btn-outline-success w-100 py-3">
                                    <i class="fas fa-file-invoice fa-2x mb-2 d-block"></i>
                                    إدارة الفواتير
                                    <small class="d-block text-muted">إنشاء وإدارة الفواتير</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/expenses" class="btn btn-outline-info w-100 py-3">
                                    <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                                    إدارة المصروفات
                                    <small class="d-block text-muted">تسجيل المصروفات</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/payroll" class="btn btn-outline-warning w-100 py-3">
                                    <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                    إدارة الرواتب
                                    <small class="d-block text-muted">رواتب الموظفين</small>
                                </a>
                            </div>
                        </div>

                        <div class="row g-3 mt-2">
                            <div class="col-md-3">
                                <a href="/e-invoice" class="btn btn-outline-danger w-100 py-3">
                                    <i class="fas fa-file-invoice-dollar fa-2x mb-2 d-block"></i>
                                    الفاتورة الإلكترونية
                                    <small class="d-block text-muted">متوافق مع زاتكا</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/branches" class="btn btn-outline-secondary w-100 py-3">
                                    <i class="fas fa-building fa-2x mb-2 d-block"></i>
                                    إدارة الفروع
                                    <small class="d-block text-muted">نظام متعدد الفروع</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/notifications" class="btn btn-outline-dark w-100 py-3">
                                    <i class="fas fa-bell fa-2x mb-2 d-block"></i>
                                    الإشعارات
                                    <small class="d-block text-muted">تنبيهات النظام</small>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/settings" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                    الإعدادات
                                    <small class="d-block text-muted">نسخ احتياطية</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''')

    return render_template_string(dashboard_template,
                                user_name=session.get('user_name', 'مستخدم'),
                                user_role=session.get('user_role', 'مستخدم'),
                                total_customers=stats['total_customers'],
                                total_products=stats['total_products'],
                                total_invoices=stats['total_invoices'],
                                low_stock_products=stats['low_stock_products'],
                                total_revenue=stats['total_revenue'],
                                total_expenses=stats['total_expenses'],
                                net_profit=stats['net_profit'],
                                recent_activities=recent_activities,
                                session=session)

# صفحة إدارة المنتجات
@app.route('/products')
def products():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب المنتجات مع معلومات الفئات والموردين
    products = conn.execute('''
        SELECT p.*, c.name as category_name, s.name as supplier_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.is_active = 1
        ORDER BY p.name
    ''').fetchall()

    # جلب الفئات والموردين للقوائم المنسدلة
    categories = conn.execute('SELECT * FROM categories WHERE is_active = 1 ORDER BY name').fetchall()
    suppliers = conn.execute('SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name').fetchall()

    # إحصائيات المنتجات
    stats = {
        'total_products': len(products),
        'low_stock': len([p for p in products if p['current_stock'] <= p['min_stock_level']]),
        'out_of_stock': len([p for p in products if p['current_stock'] == 0]),
        'total_value': sum(p['current_stock'] * p['cost_price'] for p in products)
    }

    conn.close()

    products_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-box me-2 text-primary"></i>إدارة المنتجات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات المنتجات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_products }}</div>
                    <div class="text-muted">إجمالي المنتجات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <div class="stats-number">{{ stats.low_stock }}</div>
                    <div class="text-muted">منتجات منخفضة المخزون</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-times-circle text-danger"></i>
                    </div>
                    <div class="stats-number">{{ stats.out_of_stock }}</div>
                    <div class="text-muted">منتجات نفدت</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave text-success"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_value) }}</div>
                    <div class="text-muted">قيمة المخزون (ر.س)</div>
                </div>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">قائمة المنتجات</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="البحث في المنتجات..." id="searchProducts">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الفئة</th>
                                        <th>المورد</th>
                                        <th>المخزون</th>
                                        <th>سعر التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in products %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="product-icon me-3">
                                                    <i class="fas fa-box text-primary"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ product.name }}</h6>
                                                    <small class="text-muted">{{ product.sku or 'بدون رمز' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ product.category_name or 'غير محدد' }}</td>
                                        <td>{{ product.supplier_name or 'غير محدد' }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">{{ product.current_stock }} {{ product.unit }}</span>
                                                {% if product.current_stock == 0 %}
                                                    <span class="badge bg-danger">نفد</span>
                                                {% elif product.current_stock <= product.min_stock_level %}
                                                    <span class="badge bg-warning">منخفض</span>
                                                {% else %}
                                                    <span class="badge bg-success">متوفر</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>{{ "{:,.2f}".format(product.cost_price) }} ر.س</td>
                                        <td>{{ "{:,.2f}".format(product.selling_price) }} ر.س</td>
                                        <td>
                                            {% if product.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-secondary">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض" onclick="viewProduct({{ product.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if session.user_role in ['admin', 'manager'] %}
                                                <button class="btn btn-outline-success" title="تعديل" onclick="editProduct({{ product.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteProduct({{ product.id }}, '{{ product.name }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة منتج جديد -->
        <div class="modal fade" id="addProductModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة منتج جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addProductForm" method="POST" action="/products/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رمز المنتج</label>
                                    <input type="text" class="form-control" name="sku">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الباركود</label>
                                    <input type="text" class="form-control" name="barcode">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select class="form-control" name="category_id">
                                        <option value="">اختر الفئة</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المورد</label>
                                    <select class="form-control" name="supplier_id">
                                        <option value="">اختر المورد</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الوحدة</label>
                                    <select class="form-control" name="unit">
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلو">كيلو</option>
                                        <option value="لتر">لتر</option>
                                        <option value="علبة">علبة</option>
                                        <option value="زجاجة">زجاجة</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سعر التكلفة *</label>
                                    <input type="number" step="0.01" class="form-control" name="cost_price" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سعر البيع *</label>
                                    <input type="number" step="0.01" class="form-control" name="selling_price" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">المخزون الحالي</label>
                                    <input type="number" class="form-control" name="current_stock" value="0">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأدنى</label>
                                    <input type="number" class="form-control" name="min_stock_level" value="0">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأقصى</label>
                                    <input type="number" class="form-control" name="max_stock_level" value="0">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addProductForm">حفظ المنتج</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // وظيفة البحث في المنتجات
            document.getElementById('searchProducts').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const productName = row.querySelector('h6').textContent.toLowerCase();
                    const category = row.cells[1].textContent.toLowerCase();
                    const supplier = row.cells[2].textContent.toLowerCase();

                    if (productName.includes(searchTerm) ||
                        category.includes(searchTerm) ||
                        supplier.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });

            // إغلاق النافذة بعد الحفظ الناجح
            document.getElementById('addProductForm').addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
                submitBtn.disabled = true;
            });

            // وظائف الإجراءات
            function viewProduct(productId) {
                fetch(`/products/${productId}/view`)
                    .then(response => response.json())
                    .then(data => {
                        alert(`المنتج: ${data.name}\\nالفئة: ${data.category || 'غير محدد'}\\nالمخزون: ${data.current_stock} ${data.unit}\\nسعر البيع: ${data.selling_price} ر.س`);
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء عرض المنتج');
                    });
            }

            function editProduct(productId) {
                fetch(`/products/${productId}/edit`)
                    .then(response => response.json())
                    .then(data => {
                        // ملء النموذج بالبيانات الحالية
                        document.querySelector('#addProductForm input[name="name"]').value = data.name || '';
                        document.querySelector('#addProductForm input[name="sku"]').value = data.sku || '';
                        document.querySelector('#addProductForm input[name="cost_price"]').value = data.cost_price || '';
                        document.querySelector('#addProductForm input[name="selling_price"]').value = data.selling_price || '';
                        document.querySelector('#addProductForm input[name="current_stock"]').value = data.current_stock || '';
                        document.querySelector('#addProductForm textarea[name="description"]').value = data.description || '';

                        // تغيير عنوان النافذة والنموذج
                        document.querySelector('#addProductModal .modal-title').textContent = 'تعديل المنتج';
                        document.querySelector('#addProductForm').action = `/products/${productId}/edit`;

                        // إظهار النافذة
                        new bootstrap.Modal(document.getElementById('addProductModal')).show();
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء تحميل بيانات المنتج');
                    });
            }

            function deleteProduct(productId, productName) {
                if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
                    fetch(`/products/${productId}/delete`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء حذف المنتج');
                        }
                    });
                }
            }

            // إعادة تعيين النموذج عند إغلاق النافذة
            document.getElementById('addProductModal').addEventListener('hidden.bs.modal', function() {
                document.querySelector('#addProductForm').reset();
                document.querySelector('#addProductModal .modal-title').textContent = 'إضافة منتج جديد';
                document.querySelector('#addProductForm').action = '/products/add';
            });
        </script>
    ''')

    return render_template_string(products_template,
                                products=products,
                                categories=categories,
                                suppliers=suppliers,
                                stats=stats,
                                session=session)

# صفحة إدارة العملاء
@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب العملاء
    customers = conn.execute('''
        SELECT * FROM customers
        WHERE is_active = 1
        ORDER BY name
    ''').fetchall()

    # إحصائيات العملاء
    stats = {
        'total_customers': len(customers),
        'companies': len([c for c in customers if c['customer_type'] == 'company']),
        'individuals': len([c for c in customers if c['customer_type'] == 'individual']),
        'total_credit_limit': sum(c['credit_limit'] for c in customers)
    }

    conn.close()

    customers_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-users me-2 text-primary"></i>إدارة العملاء</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                        <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات العملاء -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_customers }}</div>
                    <div class="text-muted">إجمالي العملاء</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-building text-info"></i>
                    </div>
                    <div class="stats-number">{{ stats.companies }}</div>
                    <div class="text-muted">شركات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-user text-success"></i>
                    </div>
                    <div class="stats-number">{{ stats.individuals }}</div>
                    <div class="text-muted">أفراد</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-credit-card text-warning"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_credit_limit) }}</div>
                    <div class="text-muted">إجمالي الحدود الائتمانية</div>
                </div>
            </div>
        </div>

        <!-- جدول العملاء -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">قائمة العملاء</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="البحث في العملاء..." id="searchCustomers">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>النوع</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الحد الائتماني</th>
                                        <th>نسبة الخصم</th>
                                        <th>نقاط الولاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in customers %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="customer-icon me-3">
                                                    {% if customer.customer_type == 'company' %}
                                                        <i class="fas fa-building text-info"></i>
                                                    {% else %}
                                                        <i class="fas fa-user text-success"></i>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ customer.name }}</h6>
                                                    {% if customer.tax_number %}
                                                        <small class="text-muted">ض.ق: {{ customer.tax_number }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if customer.customer_type == 'company' %}
                                                <span class="badge bg-info">شركة</span>
                                            {% else %}
                                                <span class="badge bg-success">فرد</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ customer.phone or '-' }}</td>
                                        <td>{{ customer.email or '-' }}</td>
                                        <td>{{ "{:,.0f}".format(customer.credit_limit) }} ر.س</td>
                                        <td>{{ customer.discount_rate }}%</td>
                                        <td>
                                            <span class="badge bg-warning">{{ customer.loyalty_points }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض" onclick="viewCustomer({{ customer.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="تعديل" onclick="editCustomer({{ customer.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteCustomer({{ customer.id }}, '{{ customer.name }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة عميل جديد -->
        <div class="modal fade" id="addCustomerModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة عميل جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addCustomerForm" method="POST" action="/customers/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع العميل</label>
                                    <select class="form-control" name="customer_type">
                                        <option value="individual">فرد</option>
                                        <option value="company">شركة</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الحد الائتماني</label>
                                    <input type="number" step="0.01" class="form-control" name="credit_limit" value="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" step="0.01" class="form-control" name="discount_rate" value="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نقاط الولاء</label>
                                    <input type="number" class="form-control" name="loyalty_points" value="0">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addCustomerForm">حفظ العميل</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // وظيفة البحث في العملاء
            document.getElementById('searchCustomers').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const customerName = row.querySelector('h6').textContent.toLowerCase();
                    const email = row.cells[1].textContent.toLowerCase();
                    const phone = row.cells[2].textContent.toLowerCase();

                    if (customerName.includes(searchTerm) ||
                        email.includes(searchTerm) ||
                        phone.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });

            // إغلاق النافذة بعد الحفظ الناجح
            document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
                const submitBtn = document.querySelector('button[form="addCustomerForm"]');
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
                submitBtn.disabled = true;
            });

            // وظائف الإجراءات للعملاء
            function viewCustomer(customerId) {
                fetch(`/customers/${customerId}/view`)
                    .then(response => response.json())
                    .then(data => {
                        alert(`العميل: ${data.name}\\nالنوع: ${data.customer_type === 'individual' ? 'فرد' : 'شركة'}\\nالهاتف: ${data.phone || 'غير محدد'}\\nالبريد: ${data.email || 'غير محدد'}`);
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء عرض العميل');
                    });
            }

            function editCustomer(customerId) {
                fetch(`/customers/${customerId}/edit`)
                    .then(response => response.json())
                    .then(data => {
                        // ملء النموذج بالبيانات الحالية
                        document.querySelector('#addCustomerForm input[name="name"]').value = data.name || '';
                        document.querySelector('#addCustomerForm input[name="email"]').value = data.email || '';
                        document.querySelector('#addCustomerForm input[name="phone"]').value = data.phone || '';
                        document.querySelector('#addCustomerForm textarea[name="address"]').value = data.address || '';
                        document.querySelector('#addCustomerForm select[name="customer_type"]').value = data.customer_type || 'individual';

                        // تغيير عنوان النافذة والنموذج
                        document.querySelector('#addCustomerModal .modal-title').textContent = 'تعديل العميل';
                        document.querySelector('#addCustomerForm').action = `/customers/${customerId}/edit`;

                        // إظهار النافذة
                        new bootstrap.Modal(document.getElementById('addCustomerModal')).show();
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء تحميل بيانات العميل');
                    });
            }

            function deleteCustomer(customerId, customerName) {
                if (confirm(`هل أنت متأكد من حذف العميل "${customerName}"؟`)) {
                    fetch(`/customers/${customerId}/delete`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء حذف العميل');
                        }
                    });
                }
            }

            // إعادة تعيين النموذج عند إغلاق النافذة
            document.getElementById('addCustomerModal').addEventListener('hidden.bs.modal', function() {
                document.querySelector('#addCustomerForm').reset();
                document.querySelector('#addCustomerModal .modal-title').textContent = 'إضافة عميل جديد';
                document.querySelector('#addCustomerForm').action = '/customers/add';
            });
        </script>
    ''')

    return render_template_string(customers_template,
                                customers=customers,
                                stats=stats,
                                session=session)

# صفحة إدارة الفئات
@app.route('/categories')
def categories():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب الفئات مع عدد المنتجات
    categories = conn.execute('''
        SELECT c.*, COUNT(p.id) as products_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        WHERE c.is_active = 1
        GROUP BY c.id
        ORDER BY c.name
    ''').fetchall()

    conn.close()

    categories_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-tags me-2 text-primary"></i>إدارة فئات المنتجات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>إضافة فئة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- بطاقات الفئات -->
        <div class="row">
            {% for category in categories %}
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="category-icon mb-3">
                            <i class="fas fa-tag fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">{{ category.name }}</h5>
                        <p class="card-text text-muted">{{ category.description or 'بدون وصف' }}</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <span class="badge bg-info">{{ category.products_count }} منتج</span>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-primary" title="عرض المنتجات">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- نافذة إضافة فئة جديدة -->
        <div class="modal fade" id="addCategoryModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة فئة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addCategoryForm" method="POST" action="/categories/add">
                            <div class="mb-3">
                                <label class="form-label">اسم الفئة *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addCategoryForm">حفظ الفئة</button>
                    </div>
                </div>
            </div>
        </div>
    ''')

    return render_template_string(categories_template,
                                categories=categories,
                                session=session)

# صفحة إدارة الموردين
@app.route('/suppliers')
def suppliers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب الموردين مع عدد المنتجات
    suppliers = conn.execute('''
        SELECT s.*, COUNT(p.id) as products_count
        FROM suppliers s
        LEFT JOIN products p ON s.id = p.supplier_id AND p.is_active = 1
        WHERE s.is_active = 1
        GROUP BY s.id
        ORDER BY s.name
    ''').fetchall()

    conn.close()

    suppliers_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-truck me-2 text-primary"></i>إدارة الموردين</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                        <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول الموردين -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة الموردين</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>المورد</th>
                                        <th>الشخص المسؤول</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>شروط الدفع</th>
                                        <th>عدد المنتجات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for supplier in suppliers %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="supplier-icon me-3">
                                                    <i class="fas fa-truck text-primary"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ supplier.name }}</h6>
                                                    {% if supplier.tax_number %}
                                                        <small class="text-muted">ض.ق: {{ supplier.tax_number }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ supplier.contact_person or '-' }}</td>
                                        <td>{{ supplier.phone or '-' }}</td>
                                        <td>{{ supplier.email or '-' }}</td>
                                        <td>{{ supplier.payment_terms or '-' }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ supplier.products_count }} منتج</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="المنتجات">
                                                    <i class="fas fa-box"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة مورد جديد -->
        <div class="modal fade" id="addSupplierModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة مورد جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addSupplierForm" method="POST" action="/suppliers/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المورد *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الشخص المسؤول</label>
                                    <input type="text" class="form-control" name="contact_person">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">شروط الدفع</label>
                                    <input type="text" class="form-control" name="payment_terms">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addSupplierForm">حفظ المورد</button>
                    </div>
                </div>
            </div>
        </div>
    ''')

    return render_template_string(suppliers_template,
                                suppliers=suppliers,
                                session=session)

# مسار إضافة منتج جديد
@app.route('/products/add', methods=['POST'])
def add_product():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        # استلام البيانات من النموذج
        name = request.form.get('name')
        sku = request.form.get('sku')
        barcode = request.form.get('barcode')
        description = request.form.get('description')
        unit = request.form.get('unit', 'قطعة')
        cost_price = float(request.form.get('cost_price', 0))
        selling_price = float(request.form.get('selling_price', 0))
        current_stock = int(request.form.get('current_stock', 0))
        min_stock_level = int(request.form.get('min_stock_level', 0))
        max_stock_level = int(request.form.get('max_stock_level', 0))
        category_id = request.form.get('category_id')
        supplier_id = request.form.get('supplier_id')

        # التحقق من البيانات المطلوبة
        if not name or cost_price < 0 or selling_price < 0:
            flash('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'error')
            return redirect(url_for('products'))

        # إدراج المنتج في قاعدة البيانات
        conn = get_db_connection()
        cursor = conn.execute('''
            INSERT INTO products (name, sku, barcode, description, unit, cost_price,
                                selling_price, current_stock, min_stock_level, max_stock_level,
                                category_id, supplier_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, sku, barcode, description, unit, cost_price, selling_price,
              current_stock, min_stock_level, max_stock_level, category_id, supplier_id,
              datetime.now(), datetime.now()))

        product_id = cursor.lastrowid

        # تسجيل في سجل التدقيق
        log_audit('إضافة منتج', 'products', product_id, None, {
            'name': name, 'sku': sku, 'cost_price': cost_price, 'selling_price': selling_price, 'current_stock': current_stock
        })

        conn.commit()
        conn.close()

        flash(f'تم إضافة المنتج "{name}" بنجاح!', 'success')
        return redirect(url_for('products'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة المنتج: {str(e)}', 'error')
        return redirect(url_for('products'))

# مسار عرض منتج
@app.route('/products/<int:product_id>/view')
def view_product(product_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()
    product = conn.execute('''
        SELECT p.*, c.name as category_name, s.name as supplier_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.id = ?
    ''', (product_id,)).fetchone()
    conn.close()

    if not product:
        flash('المنتج غير موجود', 'error')
        return redirect(url_for('products'))

    return jsonify({
        'id': product['id'],
        'name': product['name'],
        'sku': product['sku'],
        'category': product['category_name'],
        'supplier': product['supplier_name'],
        'cost_price': product['cost_price'],
        'selling_price': product['selling_price'],
        'current_stock': product['current_stock'],
        'unit': product['unit'],
        'description': product['description']
    })

# مسار تعديل منتج
@app.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
def edit_product(product_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    if request.method == 'POST':
        try:
            name = request.form.get('name')
            sku = request.form.get('sku')
            cost_price = float(request.form.get('cost_price', 0))
            selling_price = float(request.form.get('selling_price', 0))
            current_stock = int(request.form.get('current_stock', 0))
            description = request.form.get('description')

            conn.execute('''
                UPDATE products
                SET name = ?, sku = ?, cost_price = ?, selling_price = ?,
                    current_stock = ?, description = ?, updated_at = ?
                WHERE id = ?
            ''', (name, sku, cost_price, selling_price, current_stock,
                  description, datetime.now(), product_id))

            conn.commit()
            conn.close()

            flash(f'تم تحديث المنتج "{name}" بنجاح!', 'success')
            return redirect(url_for('products'))

        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث المنتج: {str(e)}', 'error')
            return redirect(url_for('products'))

    # GET request - إرجاع بيانات المنتج للتعديل
    product = conn.execute('SELECT * FROM products WHERE id = ?', (product_id,)).fetchone()
    conn.close()

    if not product:
        flash('المنتج غير موجود', 'error')
        return redirect(url_for('products'))

    return jsonify(dict(product))

# مسار حذف منتج
@app.route('/products/<int:product_id>/delete', methods=['POST'])
def delete_product(product_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        conn = get_db_connection()

        # الحصول على اسم المنتج قبل الحذف
        product = conn.execute('SELECT name FROM products WHERE id = ?', (product_id,)).fetchone()

        if not product:
            flash('المنتج غير موجود', 'error')
            return redirect(url_for('products'))

        # حذف منطقي (تعطيل المنتج)
        conn.execute('UPDATE products SET is_active = 0 WHERE id = ?', (product_id,))
        conn.commit()
        conn.close()

        flash(f'تم حذف المنتج "{product["name"]}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء حذف المنتج: {str(e)}', 'error')

    return redirect(url_for('products'))

# مسار إضافة عميل جديد
@app.route('/customers/add', methods=['POST'])
def add_customer():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        # استلام البيانات من النموذج
        name = request.form.get('name')
        customer_type = request.form.get('customer_type', 'individual')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        tax_number = request.form.get('tax_number')
        credit_limit = float(request.form.get('credit_limit', 0))
        discount_rate = float(request.form.get('discount_rate', 0))
        loyalty_points = int(request.form.get('loyalty_points', 0))
        notes = request.form.get('notes')

        # التحقق من البيانات المطلوبة
        if not name:
            flash('اسم العميل مطلوب', 'error')
            return redirect(url_for('customers'))

        # إدراج العميل في قاعدة البيانات
        conn = get_db_connection()
        conn.execute('''
            INSERT INTO customers (name, customer_type, email, phone, address, tax_number,
                                 credit_limit, discount_rate, loyalty_points, notes, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, customer_type, email, phone, address, tax_number,
              credit_limit, discount_rate, loyalty_points, notes, datetime.now()))

        conn.commit()
        conn.close()

        flash(f'تم إضافة العميل "{name}" بنجاح!', 'success')
        return redirect(url_for('customers'))

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'error')
        return redirect(url_for('customers'))

# مسار عرض عميل
@app.route('/customers/<int:customer_id>/view')
def view_customer(customer_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()
    customer = conn.execute('SELECT * FROM customers WHERE id = ?', (customer_id,)).fetchone()
    conn.close()

    if not customer:
        flash('العميل غير موجود', 'error')
        return redirect(url_for('customers'))

    return jsonify(dict(customer))

# مسار تعديل عميل
@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
def edit_customer(customer_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    if request.method == 'POST':
        try:
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')
            address = request.form.get('address')
            customer_type = request.form.get('customer_type', 'individual')

            conn.execute('''
                UPDATE customers
                SET name = ?, email = ?, phone = ?, address = ?, customer_type = ?
                WHERE id = ?
            ''', (name, email, phone, address, customer_type, customer_id))

            conn.commit()
            conn.close()

            flash(f'تم تحديث العميل "{name}" بنجاح!', 'success')
            return redirect(url_for('customers'))

        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث العميل: {str(e)}', 'error')
            return redirect(url_for('customers'))

    # GET request
    customer = conn.execute('SELECT * FROM customers WHERE id = ?', (customer_id,)).fetchone()
    conn.close()

    if not customer:
        flash('العميل غير موجود', 'error')
        return redirect(url_for('customers'))

    return jsonify(dict(customer))

# مسار حذف عميل
@app.route('/customers/<int:customer_id>/delete', methods=['POST'])
def delete_customer(customer_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        conn = get_db_connection()

        customer = conn.execute('SELECT name FROM customers WHERE id = ?', (customer_id,)).fetchone()

        if not customer:
            flash('العميل غير موجود', 'error')
            return redirect(url_for('customers'))

        # حذف منطقي
        conn.execute('UPDATE customers SET is_active = 0 WHERE id = ?', (customer_id,))
        conn.commit()
        conn.close()

        flash(f'تم حذف العميل "{customer["name"]}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء حذف العميل: {str(e)}', 'error')

    return redirect(url_for('customers'))

# مسار إضافة فئة جديدة
@app.route('/categories/add', methods=['POST'])
def add_category():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        name = request.form.get('name')
        description = request.form.get('description')

        if not name:
            flash('اسم الفئة مطلوب', 'error')
            return redirect(url_for('categories'))

        conn = get_db_connection()
        conn.execute('''
            INSERT INTO categories (name, description, created_at)
            VALUES (?, ?, ?)
        ''', (name, description, datetime.now()))

        conn.commit()
        conn.close()

        flash(f'تم إضافة الفئة "{name}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة الفئة: {str(e)}', 'error')

    return redirect(url_for('categories'))

# مسار إضافة مورد جديد
@app.route('/suppliers/add', methods=['POST'])
def add_supplier():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        tax_number = request.form.get('tax_number')
        payment_terms = request.form.get('payment_terms')
        notes = request.form.get('notes')

        if not name:
            flash('اسم المورد مطلوب', 'error')
            return redirect(url_for('suppliers'))

        conn = get_db_connection()
        conn.execute('''
            INSERT INTO suppliers (name, contact_person, email, phone, address,
                                 tax_number, payment_terms, notes, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, contact_person, email, phone, address, tax_number,
              payment_terms, notes, datetime.now()))

        conn.commit()
        conn.close()

        flash(f'تم إضافة المورد "{name}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة المورد: {str(e)}', 'error')

    return redirect(url_for('suppliers'))

# صفحة الفواتير
@app.route('/invoices')
def invoices():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب الفواتير مع معلومات العملاء
    invoices = conn.execute('''
        SELECT i.*, c.name as customer_name, u.first_name || ' ' || u.last_name as created_by_name
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        LEFT JOIN users u ON i.created_by = u.id
        ORDER BY i.created_at DESC
    ''').fetchall()

    # إحصائيات الفواتير
    stats = {
        'total_invoices': len(invoices),
        'draft_invoices': len([i for i in invoices if i['status'] == 'draft']),
        'paid_invoices': len([i for i in invoices if i['status'] == 'paid']),
        'total_amount': sum(i['total_amount'] for i in invoices if i['total_amount'])
    }

    conn.close()

    invoices_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-file-invoice me-2 text-primary"></i>إدارة الفواتير</h2>
                    <a href="/invoices/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات الفواتير -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_invoices }}</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-edit text-warning"></i>
                    </div>
                    <div class="stats-number">{{ stats.draft_invoices }}</div>
                    <div class="text-muted">مسودات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div class="stats-number">{{ stats.paid_invoices }}</div>
                    <div class="text-muted">مدفوعة</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave text-success"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_amount) }}</div>
                    <div class="text-muted">إجمالي المبلغ (ر.س)</div>
                </div>
            </div>
        </div>

        <!-- جدول الفواتير -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة الفواتير</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>الحالة</th>
                                        <th>أنشأها</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in invoices %}
                                    <tr>
                                        <td>
                                            <strong>{{ invoice.invoice_number }}</strong>
                                        </td>
                                        <td>{{ invoice.customer_name or 'عميل نقدي' }}</td>
                                        <td>{{ invoice.issue_date }}</td>
                                        <td>{{ "{:,.2f}".format(invoice.total_amount) }} ر.س</td>
                                        <td>
                                            {% if invoice.status == 'draft' %}
                                                <span class="badge bg-warning">مسودة</span>
                                            {% elif invoice.status == 'sent' %}
                                                <span class="badge bg-info">مرسلة</span>
                                            {% elif invoice.status == 'paid' %}
                                                <span class="badge bg-success">مدفوعة</span>
                                            {% elif invoice.status == 'overdue' %}
                                                <span class="badge bg-danger">متأخرة</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ invoice.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ invoice.created_by_name }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض" onclick="viewInvoice({{ invoice.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="تعديل" onclick="editInvoice({{ invoice.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="/invoices/{{ invoice.id }}/print" class="btn btn-outline-info" title="طباعة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.invoice_number }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // وظائف إدارة الفواتير
            function viewInvoice(invoiceId) {
                // فتح الفاتورة للطباعة في نافذة جديدة
                window.open(`/invoices/${invoiceId}/print`, '_blank');
            }

            function editInvoice(invoiceId) {
                // توجيه لصفحة تعديل الفاتورة
                window.location.href = `/invoices/${invoiceId}/edit`;
            }

            function deleteInvoice(invoiceId, invoiceNumber) {
                if (confirm(`هل أنت متأكد من حذف الفاتورة "${invoiceNumber}"؟\\n\\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
                    fetch(`/invoices/${invoiceId}/delete`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            alert('تم حذف الفاتورة بنجاح!');
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء حذف الفاتورة');
                        }
                    }).catch(error => {
                        alert('حدث خطأ أثناء حذف الفاتورة');
                    });
                }
            }
        </script>
    ''')

    return render_template_string(invoices_template,
                                invoices=invoices,
                                stats=stats,
                                session=session)

# صفحة إنشاء فاتورة جديدة
@app.route('/invoices/create')
def create_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب العملاء والمنتجات
    customers = conn.execute('SELECT * FROM customers WHERE is_active = 1 ORDER BY name').fetchall()
    products = conn.execute('SELECT * FROM products WHERE is_active = 1 ORDER BY name').fetchall()

    # إنشاء رقم فاتورة جديد
    last_invoice = conn.execute('SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1').fetchone()
    if last_invoice:
        last_number = int(last_invoice['invoice_number'].replace('INV-', ''))
        new_invoice_number = f"INV-{last_number + 1:06d}"
    else:
        new_invoice_number = "INV-000001"

    conn.close()

    create_invoice_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-plus-circle me-2 text-primary"></i>إنشاء فاتورة جديدة</h2>
                    <a href="/invoices" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للفواتير
                    </a>
                </div>
            </div>
        </div>

        <form method="POST" action="/invoices/save" id="invoiceForm">
            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">معلومات الفاتورة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoice_number" value="{{ invoice_number }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الإصدار</label>
                                    <input type="date" class="form-control" name="issue_date" value="{{ today }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" name="due_date">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العميل</label>
                                    <select class="form-control" name="customer_id" id="customerSelect">
                                        <option value="">عميل نقدي</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.id }}" data-discount="{{ customer.discount_rate }}">{{ customer.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بنود الفاتورة -->
                    <div class="card">
                        <div class="card-header bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">بنود الفاتورة</h5>
                                <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus me-1"></i>إضافة بند
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table" id="invoiceItemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الخصم %</th>
                                            <th>الإجمالي</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoiceItemsBody">
                                        <!-- سيتم إضافة البنود هنا بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">ملخص الفاتورة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0.00 ر.س</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الخصم:</span>
                                <span id="discount">0.00 ر.س</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الضريبة (15%):</span>
                                <span id="tax">0.00 ر.س</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>الإجمالي النهائي:</strong>
                                <strong id="total">0.00 ر.س</strong>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-control" name="payment_method">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة</option>
                                    <option value="transfer">تحويل</option>
                                    <option value="credit">آجل</option>
                                </select>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="action" value="draft" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>حفظ كمسودة
                                </button>
                                <button type="submit" name="action" value="send" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>إرسال الفاتورة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- قائمة المنتجات المخفية للـ JavaScript -->
        <script>
            const products = {{ products_json | safe }};
            let itemCounter = 0;

            function addInvoiceItem() {
                const tbody = document.getElementById('invoiceItemsBody');
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <select class="form-control" name="items[${itemCounter}][product_id]" onchange="updateItemPrice(this, ${itemCounter})" required>
                            <option value="">اختر المنتج</option>
                            {% for product in products %}
                            <option value="{{ product.id }}" data-price="{{ product.selling_price }}">{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control" name="items[${itemCounter}][quantity]" min="1" value="1" onchange="calculateItemTotal(${itemCounter})" required>
                    </td>
                    <td>
                        <input type="number" step="0.01" class="form-control" name="items[${itemCounter}][unit_price]" onchange="calculateItemTotal(${itemCounter})" required>
                    </td>
                    <td>
                        <input type="number" step="0.01" class="form-control" name="items[${itemCounter}][discount_rate]" value="0" onchange="calculateItemTotal(${itemCounter})">
                    </td>
                    <td>
                        <span class="item-total">0.00 ر.س</span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
                itemCounter++;
            }

            function updateItemPrice(select, index) {
                const selectedOption = select.options[select.selectedIndex];
                const price = selectedOption.getAttribute('data-price') || 0;
                const priceInput = select.closest('tr').querySelector(`input[name="items[${index}][unit_price]"]`);
                priceInput.value = price;
                calculateItemTotal(index);
            }

            function calculateItemTotal(index) {
                const row = document.querySelector(`tr:nth-child(${index + 1})`);
                const quantity = parseFloat(row.querySelector(`input[name="items[${index}][quantity]"]`).value) || 0;
                const unitPrice = parseFloat(row.querySelector(`input[name="items[${index}][unit_price]"]`).value) || 0;
                const discountRate = parseFloat(row.querySelector(`input[name="items[${index}][discount_rate]"]`).value) || 0;

                const subtotal = quantity * unitPrice;
                const discount = subtotal * (discountRate / 100);
                const total = subtotal - discount;

                row.querySelector('.item-total').textContent = total.toFixed(2) + ' ر.س';
                calculateInvoiceTotal();
            }

            function removeItem(button) {
                button.closest('tr').remove();
                calculateInvoiceTotal();
            }

            function calculateInvoiceTotal() {
                const itemTotals = document.querySelectorAll('.item-total');
                let subtotal = 0;

                itemTotals.forEach(item => {
                    const value = parseFloat(item.textContent.replace(' ر.س', '')) || 0;
                    subtotal += value;
                });

                const tax = subtotal * 0.15;
                const total = subtotal + tax;

                document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
                document.getElementById('tax').textContent = tax.toFixed(2) + ' ر.س';
                document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';
            }

            // إضافة بند واحد افتراضي
            document.addEventListener('DOMContentLoaded', function() {
                addInvoiceItem();
            });
        </script>
    ''')

    from datetime import date
    import json

    return render_template_string(create_invoice_template,
                                customers=customers,
                                products=products,
                                products_json=json.dumps([dict(p) for p in products]),
                                invoice_number=new_invoice_number,
                                today=date.today().isoformat(),
                                session=session)

# مسار حفظ الفاتورة
@app.route('/invoices/save', methods=['POST'])
def save_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        # استلام بيانات الفاتورة
        invoice_number = request.form.get('invoice_number')
        customer_id = request.form.get('customer_id') or None
        issue_date = request.form.get('issue_date')
        due_date = request.form.get('due_date') or None
        payment_method = request.form.get('payment_method')
        notes = request.form.get('notes')
        action = request.form.get('action', 'draft')

        # تحديد حالة الفاتورة
        status = 'draft' if action == 'draft' else 'sent'

        conn = get_db_connection()

        # إدراج الفاتورة
        cursor = conn.execute('''
            INSERT INTO invoices (invoice_number, customer_id, created_by, issue_date, due_date,
                                payment_method, notes, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (invoice_number, customer_id, session['user_id'], issue_date, due_date,
              payment_method, notes, status, datetime.now(), datetime.now()))

        invoice_id = cursor.lastrowid

        # معالجة بنود الفاتورة
        subtotal = 0
        tax_amount = 0

        # جمع بيانات البنود من النموذج
        items_data = {}
        for key, value in request.form.items():
            if key.startswith('items[') and '][' in key:
                # استخراج فهرس البند والحقل
                parts = key.split('][')
                index = parts[0].split('[')[1]
                field = parts[1].rstrip(']')

                if index not in items_data:
                    items_data[index] = {}
                items_data[index][field] = value

        # إدراج بنود الفاتورة
        for item_data in items_data.values():
            if item_data.get('product_id'):
                product_id = int(item_data['product_id'])
                quantity = float(item_data['quantity'])
                unit_price = float(item_data['unit_price'])
                discount_rate = float(item_data.get('discount_rate', 0))

                # حساب المبالغ
                item_subtotal = quantity * unit_price
                item_discount = item_subtotal * (discount_rate / 100)
                item_net = item_subtotal - item_discount
                item_tax = item_net * 0.15  # ضريبة 15%

                subtotal += item_net
                tax_amount += item_tax

                # إدراج البند
                conn.execute('''
                    INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_rate, tax_rate)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (invoice_id, product_id, quantity, unit_price, discount_rate, 15.0))

        # تحديث إجماليات الفاتورة
        total_amount = subtotal + tax_amount
        conn.execute('''
            UPDATE invoices
            SET subtotal = ?, tax_amount = ?, total_amount = ?
            WHERE id = ?
        ''', (subtotal, tax_amount, total_amount, invoice_id))

        conn.commit()
        conn.close()

        flash(f'تم حفظ الفاتورة {invoice_number} بنجاح!', 'success')
        return redirect(url_for('invoices'))

    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ الفاتورة: {str(e)}', 'error')
        return redirect(url_for('create_invoice'))

# مسار تعديل فاتورة
@app.route('/invoices/<int:invoice_id>/edit')
def edit_invoice_page(invoice_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب بيانات الفاتورة
    invoice = conn.execute('''
        SELECT i.*, c.name as customer_name
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        WHERE i.id = ?
    ''', (invoice_id,)).fetchone()

    if not invoice:
        flash('الفاتورة غير موجودة', 'error')
        return redirect(url_for('invoices'))

    # جلب بنود الفاتورة
    items = conn.execute('''
        SELECT ii.*, p.name as product_name
        FROM invoice_items ii
        JOIN products p ON ii.product_id = p.id
        WHERE ii.invoice_id = ?
    ''', (invoice_id,)).fetchall()

    conn.close()

    # إعادة توجيه لصفحة إنشاء فاتورة مع البيانات المحملة
    flash(f'تعديل الفاتورة {invoice["invoice_number"]} - قريباً', 'info')
    return redirect(url_for('invoices'))

# مسار حذف فاتورة
@app.route('/invoices/<int:invoice_id>/delete', methods=['POST'])
def delete_invoice(invoice_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin', 'manager']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        conn = get_db_connection()

        # التحقق من وجود الفاتورة
        invoice = conn.execute('SELECT invoice_number FROM invoices WHERE id = ?', (invoice_id,)).fetchone()

        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404

        # حذف بنود الفاتورة أولاً
        conn.execute('DELETE FROM invoice_items WHERE invoice_id = ?', (invoice_id,))

        # حذف الفاتورة
        conn.execute('DELETE FROM invoices WHERE id = ?', (invoice_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# صفحة إدارة المصروفات
@app.route('/expenses')
def expenses():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب المصروفات
    expenses = conn.execute('''
        SELECT e.*, u.first_name || ' ' || u.last_name as created_by_name,
               a.first_name || ' ' || a.last_name as approved_by_name
        FROM expenses e
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN users a ON e.approved_by = a.id
        ORDER BY e.created_at DESC
    ''').fetchall()

    # إحصائيات المصروفات
    stats = {
        'total_expenses': len(expenses),
        'pending_expenses': len([e for e in expenses if e['status'] == 'pending']),
        'approved_expenses': len([e for e in expenses if e['status'] == 'approved']),
        'total_amount': sum(e['amount'] for e in expenses if e['amount'])
    }

    conn.close()

    expenses_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-receipt me-2 text-primary"></i>إدارة المصروفات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                        <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات المصروفات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_expenses }}</div>
                    <div class="text-muted">إجمالي المصروفات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-clock text-warning"></i>
                    </div>
                    <div class="stats-number">{{ stats.pending_expenses }}</div>
                    <div class="text-muted">في انتظار الموافقة</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div class="stats-number">{{ stats.approved_expenses }}</div>
                    <div class="text-muted">موافق عليها</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave text-danger"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_amount) }}</div>
                    <div class="text-muted">إجمالي المبلغ (ر.س)</div>
                </div>
            </div>
        </div>

        <!-- جدول المصروفات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة المصروفات</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم المرجع</th>
                                        <th>العنوان</th>
                                        <th>الفئة</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>أنشأها</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in expenses %}
                                    <tr>
                                        <td><strong>{{ expense.reference_number }}</strong></td>
                                        <td>{{ expense.title }}</td>
                                        <td>{{ expense.category }}</td>
                                        <td>{{ "{:,.2f}".format(expense.amount) }} ر.س</td>
                                        <td>{{ expense.expense_date }}</td>
                                        <td>
                                            {% if expense.status == 'pending' %}
                                                <span class="badge bg-warning">في انتظار الموافقة</span>
                                            {% elif expense.status == 'approved' %}
                                                <span class="badge bg-success">موافق عليه</span>
                                            {% elif expense.status == 'paid' %}
                                                <span class="badge bg-info">مدفوع</span>
                                            {% elif expense.status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوض</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ expense.created_by_name }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض" onclick="viewExpense({{ expense.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if expense.status == 'pending' and session.user_role in ['admin', 'manager'] %}
                                                <button class="btn btn-outline-success" title="موافقة" onclick="approveExpense({{ expense.id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="رفض" onclick="rejectExpense({{ expense.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة مصروف جديد -->
        <div class="modal fade" id="addExpenseModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة مصروف جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addExpenseForm" method="POST" action="/expenses/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">عنوان المصروف *</label>
                                    <input type="text" class="form-control" name="title" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الفئة *</label>
                                    <select class="form-control" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="مصروفات تشغيلية">مصروفات تشغيلية</option>
                                        <option value="رواتب وأجور">رواتب وأجور</option>
                                        <option value="إيجارات">إيجارات</option>
                                        <option value="مرافق">مرافق</option>
                                        <option value="صيانة">صيانة</option>
                                        <option value="تسويق">تسويق</option>
                                        <option value="مصروفات إدارية">مصروفات إدارية</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المبلغ *</label>
                                    <input type="number" step="0.01" class="form-control" name="amount" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ المصروف</label>
                                    <input type="date" class="form-control" name="expense_date" value="{{ today }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المورد/الجهة</label>
                                    <input type="text" class="form-control" name="vendor">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-control" name="payment_method">
                                        <option value="cash">نقدي</option>
                                        <option value="card">بطاقة</option>
                                        <option value="transfer">تحويل</option>
                                        <option value="check">شيك</option>
                                    </select>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addExpenseForm">حفظ المصروف</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function approveExpense(expenseId) {
                if (confirm('هل تريد الموافقة على هذا المصروف؟')) {
                    fetch(`/expenses/${expenseId}/approve`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        }
                    });
                }
            }

            function rejectExpense(expenseId) {
                if (confirm('هل تريد رفض هذا المصروف؟')) {
                    fetch(`/expenses/${expenseId}/reject`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        }
                    });
                }
            }

            function viewExpense(expenseId) {
                fetch(`/expenses/${expenseId}/view`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            alert('خطأ: ' + data.error);
                        } else {
                            alert(`المصروف: ${data.title}\\nالفئة: ${data.category}\\nالمبلغ: ${data.amount} ر.س\\nالتاريخ: ${data.expense_date}\\nالحالة: ${data.status}\\nالوصف: ${data.description || 'لا يوجد'}`);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء عرض المصروف');
                    });
            }
        </script>
    ''')

    from datetime import date

    return render_template_string(expenses_template,
                                expenses=expenses,
                                stats=stats,
                                today=date.today().isoformat(),
                                session=session)

# مسار إضافة مصروف جديد
@app.route('/expenses/add', methods=['POST'])
def add_expense():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        # إنشاء رقم مرجع جديد
        conn = get_db_connection()
        last_expense = conn.execute('SELECT reference_number FROM expenses ORDER BY id DESC LIMIT 1').fetchone()
        if last_expense:
            last_number = int(last_expense['reference_number'].replace('EXP-', ''))
            reference_number = f"EXP-{last_number + 1:06d}"
        else:
            reference_number = "EXP-000001"

        # استلام البيانات
        title = request.form.get('title')
        category = request.form.get('category')
        amount = float(request.form.get('amount'))
        expense_date = request.form.get('expense_date')
        vendor = request.form.get('vendor')
        payment_method = request.form.get('payment_method')
        description = request.form.get('description')
        notes = request.form.get('notes')

        # إدراج المصروف
        conn.execute('''
            INSERT INTO expenses (reference_number, title, description, amount, category, vendor,
                                expense_date, payment_method, notes, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (reference_number, title, description, amount, category, vendor,
              expense_date, payment_method, notes, session['user_id'], datetime.now()))

        conn.commit()
        conn.close()

        flash(f'تم إضافة المصروف {reference_number} بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة المصروف: {str(e)}', 'error')

    return redirect(url_for('expenses'))

# مسار الموافقة على المصروف
@app.route('/expenses/<int:expense_id>/approve', methods=['POST'])
def approve_expense(expense_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin', 'manager']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        conn = get_db_connection()
        conn.execute('''
            UPDATE expenses
            SET status = 'approved', approved_by = ?, approved_at = ?
            WHERE id = ?
        ''', (session['user_id'], datetime.now(), expense_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# مسار رفض المصروف
@app.route('/expenses/<int:expense_id>/reject', methods=['POST'])
def reject_expense(expense_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin', 'manager']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        conn = get_db_connection()
        conn.execute('''
            UPDATE expenses
            SET status = 'rejected', approved_by = ?, approved_at = ?
            WHERE id = ?
        ''', (session['user_id'], datetime.now(), expense_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# مسار عرض مصروف
@app.route('/expenses/<int:expense_id>/view')
def view_expense(expense_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        expense = conn.execute('SELECT * FROM expenses WHERE id = ?', (expense_id,)).fetchone()
        conn.close()

        if not expense:
            return jsonify({'error': 'المصروف غير موجود'}), 404

        return jsonify(dict(expense))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# صفحة إدارة الرواتب
@app.route('/payroll')
def payroll():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب الموظفين
    employees = conn.execute('SELECT * FROM employees WHERE is_active = 1 ORDER BY first_name').fetchall()

    # إحصائيات الموظفين
    stats = {
        'total_employees': len(employees),
        'total_basic_salary': sum(e['basic_salary'] for e in employees),
        'total_gross_salary': sum(e['basic_salary'] + e['housing_allowance'] + e['transport_allowance'] + e['other_allowances'] for e in employees),
        'average_salary': sum(e['basic_salary'] for e in employees) / len(employees) if employees else 0
    }

    conn.close()

    payroll_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-users me-2 text-primary"></i>إدارة الرواتب</h2>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                            <i class="fas fa-user-plus me-2"></i>إضافة موظف
                        </button>
                        <button class="btn btn-primary" onclick="generatePayroll()">
                            <i class="fas fa-calculator me-2"></i>إنشاء كشف رواتب
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الرواتب -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_employees }}</div>
                    <div class="text-muted">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave text-success"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_basic_salary) }}</div>
                    <div class="text-muted">إجمالي الرواتب الأساسية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-chart-line text-info"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.total_gross_salary) }}</div>
                    <div class="text-muted">إجمالي الرواتب الإجمالية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-calculator text-warning"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(stats.average_salary) }}</div>
                    <div class="text-muted">متوسط الراتب</div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة الموظفين</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الموظف</th>
                                        <th>الاسم</th>
                                        <th>المنصب</th>
                                        <th>القسم</th>
                                        <th>تاريخ التوظيف</th>
                                        <th>الراتب الأساسي</th>
                                        <th>الراتب الإجمالي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for employee in employees %}
                                    <tr>
                                        <td><strong>{{ employee.employee_id }}</strong></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="employee-icon me-3">
                                                    <i class="fas fa-user text-primary"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ employee.first_name }} {{ employee.last_name }}</h6>
                                                    <small class="text-muted">{{ employee.email or 'بدون بريد' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ employee.position }}</td>
                                        <td>{{ employee.department or '-' }}</td>
                                        <td>{{ employee.hire_date }}</td>
                                        <td>{{ "{:,.0f}".format(employee.basic_salary) }} ر.س</td>
                                        <td>{{ "{:,.0f}".format(employee.basic_salary + employee.housing_allowance + employee.transport_allowance + employee.other_allowances) }} ر.س</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="كشف راتب">
                                                    <i class="fas fa-file-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة موظف جديد -->
        <div class="modal fade" id="addEmployeeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة موظف جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addEmployeeForm" method="POST" action="/employees/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الموظف *</label>
                                    <input type="text" class="form-control" name="employee_id" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الأول *</label>
                                    <input type="text" class="form-control" name="first_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الأخير *</label>
                                    <input type="text" class="form-control" name="last_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الهوية</label>
                                    <input type="text" class="form-control" name="national_id">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المنصب *</label>
                                    <input type="text" class="form-control" name="position" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">القسم</label>
                                    <input type="text" class="form-control" name="department">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ التوظيف *</label>
                                    <input type="date" class="form-control" name="hire_date" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع التوظيف</label>
                                    <select class="form-control" name="employment_type">
                                        <option value="full_time">دوام كامل</option>
                                        <option value="part_time">دوام جزئي</option>
                                        <option value="contract">عقد</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الراتب الأساسي *</label>
                                    <input type="number" step="0.01" class="form-control" name="basic_salary" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">بدل السكن</label>
                                    <input type="number" step="0.01" class="form-control" name="housing_allowance" value="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">بدل المواصلات</label>
                                    <input type="number" step="0.01" class="form-control" name="transport_allowance" value="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">بدلات أخرى</label>
                                    <input type="number" step="0.01" class="form-control" name="other_allowances" value="0">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addEmployeeForm">حفظ الموظف</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function generatePayroll() {
                if (confirm('هل تريد إنشاء كشف رواتب للشهر الحالي؟')) {
                    window.location.href = '/payroll/generate';
                }
            }
        </script>
    ''')

    return render_template_string(payroll_template,
                                employees=employees,
                                stats=stats,
                                session=session)

# مسار إضافة موظف جديد
@app.route('/employees/add', methods=['POST'])
def add_employee():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        # استلام البيانات
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        national_id = request.form.get('national_id')
        position = request.form.get('position')
        department = request.form.get('department')
        hire_date = request.form.get('hire_date')
        employment_type = request.form.get('employment_type', 'full_time')
        basic_salary = float(request.form.get('basic_salary'))
        housing_allowance = float(request.form.get('housing_allowance', 0))
        transport_allowance = float(request.form.get('transport_allowance', 0))
        other_allowances = float(request.form.get('other_allowances', 0))
        address = request.form.get('address')
        notes = request.form.get('notes')

        # التحقق من عدم تكرار رقم الموظف
        conn = get_db_connection()
        existing = conn.execute('SELECT id FROM employees WHERE employee_id = ?', (employee_id,)).fetchone()
        if existing:
            flash('رقم الموظف موجود مسبقاً', 'error')
            return redirect(url_for('payroll'))

        # إدراج الموظف
        conn.execute('''
            INSERT INTO employees (employee_id, first_name, last_name, email, phone, national_id,
                                 position, department, hire_date, employment_type, basic_salary,
                                 housing_allowance, transport_allowance, other_allowances,
                                 address, notes, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (employee_id, first_name, last_name, email, phone, national_id,
              position, department, hire_date, employment_type, basic_salary,
              housing_allowance, transport_allowance, other_allowances,
              address, notes, datetime.now()))

        conn.commit()
        conn.close()

        flash(f'تم إضافة الموظف {first_name} {last_name} بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة الموظف: {str(e)}', 'error')

    return redirect(url_for('payroll'))

# مسار إنشاء كشف رواتب
@app.route('/payroll/generate')
def generate_payroll():
    if 'user_id' not in session or session.get('user_role') not in ['admin', 'manager']:
        flash('غير مصرح لك بإنشاء كشف رواتب', 'error')
        return redirect(url_for('payroll'))

    try:
        conn = get_db_connection()

        # جلب جميع الموظفين النشطين
        employees = conn.execute('SELECT * FROM employees WHERE is_active = 1').fetchall()

        if not employees:
            flash('لا يوجد موظفين لإنشاء كشف رواتب', 'warning')
            return redirect(url_for('payroll'))

        # إنشاء كشف رواتب للشهر الحالي
        from datetime import date
        current_month = date.today().strftime('%Y-%m')

        # التحقق من وجود كشف رواتب للشهر الحالي
        existing_payroll = conn.execute('''
            SELECT COUNT(*) as count FROM payroll_records
            WHERE payroll_month = ?
        ''', (current_month,)).fetchone()

        if existing_payroll and existing_payroll['count'] > 0:
            flash(f'كشف رواتب شهر {current_month} موجود بالفعل', 'warning')
            return redirect(url_for('payroll'))

        # إنشاء جدول كشف الرواتب إذا لم يكن موجود
        conn.execute('''
            CREATE TABLE IF NOT EXISTS payroll_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER,
                payroll_month TEXT,
                basic_salary REAL,
                allowances REAL,
                deductions REAL,
                net_salary REAL,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')

        # إنشاء سجلات الرواتب
        total_payroll = 0
        for employee in employees:
            basic_salary = employee['basic_salary']
            allowances = employee['housing_allowance'] + employee['transport_allowance'] + employee['other_allowances']
            deductions = basic_salary * 0.09  # خصم التأمينات الاجتماعية 9%
            net_salary = basic_salary + allowances - deductions

            conn.execute('''
                INSERT INTO payroll_records (employee_id, payroll_month, basic_salary, allowances, deductions, net_salary, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (employee['id'], current_month, basic_salary, allowances, deductions, net_salary, session['user_id']))

            total_payroll += net_salary

        conn.commit()
        conn.close()

        flash(f'تم إنشاء كشف رواتب شهر {current_month} بنجاح! إجمالي الرواتب: {total_payroll:,.2f} ر.س', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء كشف الرواتب: {str(e)}', 'error')

    return redirect(url_for('payroll'))

# صفحة سجل التدقيق
@app.route('/audit-log')
def audit_log():
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        flash('غير مصرح لك بعرض سجل التدقيق', 'error')
        return redirect(url_for('dashboard'))

    try:
        conn = get_db_connection()

        # جلب سجلات التدقيق مع ترقيم الصفحات
        page = request.args.get('page', 1, type=int)
        per_page = 50
        offset = (page - 1) * per_page

        audit_records = conn.execute('''
            SELECT * FROM audit_log
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        ''', (per_page, offset)).fetchall()

        # عدد السجلات الإجمالي
        total_records = conn.execute('SELECT COUNT(*) as count FROM audit_log').fetchone()['count']

        conn.close()

        # حساب معلومات الترقيم
        total_pages = (total_records + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages

    except Exception as e:
        flash(f'حدث خطأ أثناء جلب سجل التدقيق: {str(e)}', 'error')
        audit_records = []
        total_records = 0
        total_pages = 0
        has_prev = False
        has_next = False

    audit_template = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>سجل التدقيق - نظام المحاسبة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
            .navbar-brand { font-weight: bold; }
            .card { border: none; box-shadow: 0 0 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .table th { background-color: #2E86AB; color: white; border: none; }
            .badge-action { font-size: 0.8em; }
            .audit-details { font-size: 0.9em; color: #666; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #2E86AB, #A23B72);">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="fas fa-chart-line me-2"></i>نظام المحاسبة
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">لوحة التحكم</a>
                    <a class="nav-link" href="/logout">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="card-title mb-1">
                                        <i class="fas fa-shield-alt text-primary me-2"></i>سجل التدقيق
                                    </h4>
                                    <p class="text-muted mb-0">تتبع جميع العمليات والتغييرات في النظام</p>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info fs-6">{{ total_records }} سجل</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audit Log Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ والوقت</th>
                                            <th>المستخدم</th>
                                            <th>العملية</th>
                                            <th>الجدول</th>
                                            <th>رقم السجل</th>
                                            <th>عنوان IP</th>
                                            <th>التفاصيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if audit_records %}
                                        {% for record in audit_records %}
                                        <tr>
                                            <td>
                                                <small class="text-muted">{{ record.timestamp }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ record.user_name }}</strong>
                                                <br><small class="text-muted">ID: {{ record.user_id }}</small>
                                            </td>
                                            <td>
                                                {% if 'إضافة' in record.action %}
                                                    <span class="badge bg-success badge-action">{{ record.action }}</span>
                                                {% elif 'تعديل' in record.action %}
                                                    <span class="badge bg-warning badge-action">{{ record.action }}</span>
                                                {% elif 'حذف' in record.action %}
                                                    <span class="badge bg-danger badge-action">{{ record.action }}</span>
                                                {% else %}
                                                    <span class="badge bg-info badge-action">{{ record.action }}</span>
                                                {% endif %}
                                            </td>
                                            <td><code>{{ record.table_name }}</code></td>
                                            <td>{{ record.record_id or '-' }}</td>
                                            <td><small>{{ record.ip_address }}</small></td>
                                            <td>
                                                {% if record.new_values %}
                                                <button class="btn btn-sm btn-outline-info" onclick="showDetails('{{ record.id }}', '{{ record.new_values|e }}', '{{ record.old_values|e }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-4">
                                                <i class="fas fa-inbox fa-2x mb-3"></i>
                                                <br>لا توجد سجلات تدقيق
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            {% if total_pages > 1 %}
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    {% if has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page - 1 }}">السابق</a>
                                    </li>
                                    {% endif %}

                                    {% for p in range(1, total_pages + 1) %}
                                    <li class="page-item {{ 'active' if p == page else '' }}">
                                        <a class="page-link" href="?page={{ p }}">{{ p }}</a>
                                    </li>
                                    {% endfor %}

                                    {% if has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page + 1 }}">التالي</a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Details Modal -->
        <div class="modal fade" id="detailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل العملية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modalContent"></div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function showDetails(recordId, newValues, oldValues) {
                try {
                    const newData = newValues ? JSON.parse(newValues) : {};
                    const oldData = oldValues ? JSON.parse(oldValues) : {};

                    let content = '<div class="row">';

                    if (Object.keys(newData).length > 0) {
                        content += '<div class="col-md-6"><h6 class="text-success">القيم الجديدة:</h6><pre class="bg-light p-2 rounded">' + JSON.stringify(newData, null, 2) + '</pre></div>';
                    }

                    if (Object.keys(oldData).length > 0) {
                        content += '<div class="col-md-6"><h6 class="text-warning">القيم السابقة:</h6><pre class="bg-light p-2 rounded">' + JSON.stringify(oldData, null, 2) + '</pre></div>';
                    }

                    content += '</div>';

                    document.getElementById('modalContent').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                } catch (e) {
                    alert('خطأ في عرض التفاصيل');
                }
            }
        </script>
    </body>
    </html>
    '''

    return render_template_string(audit_template,
                                audit_records=audit_records,
                                total_records=total_records,
                                total_pages=total_pages,
                                page=page,
                                has_prev=has_prev,
                                has_next=has_next,
                                session=session)

# صفحة إدارة الوصول عن بُعد
@app.route('/remote-access')
def remote_access():
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        flash('غير مصرح لك بإدارة الوصول عن بُعد', 'error')
        return redirect(url_for('dashboard'))

    try:
        conn = get_db_connection()

        # جلب جميع رموز الوصول
        access_tokens = conn.execute('''
            SELECT * FROM remote_access ORDER BY created_at DESC
        ''').fetchall()

        # جلب آخر الاتصالات
        recent_connections = conn.execute('''
            SELECT * FROM remote_connections
            ORDER BY timestamp DESC LIMIT 20
        ''').fetchall()

        # إحصائيات
        stats = {
            'total_tokens': len(access_tokens),
            'active_tokens': len([t for t in access_tokens if t['is_active']]),
            'total_connections': conn.execute('SELECT COUNT(*) as count FROM remote_connections').fetchone()['count']
        }

        conn.close()

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        access_tokens = []
        recent_connections = []
        stats = {'total_tokens': 0, 'active_tokens': 0, 'total_connections': 0}

    remote_template = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة الوصول عن بُعد - نظام المحاسبة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
            .card { border: none; box-shadow: 0 0 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .table th { background-color: #2E86AB; color: white; border: none; }
            .token-display { font-family: 'Courier New', monospace; background: #f8f9fa; padding: 8px; border-radius: 4px; }
            .status-active { color: #28a745; }
            .status-inactive { color: #dc3545; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #2E86AB, #A23B72);">
            <div class="container">
                <a class="navbar-brand" href="/dashboard">
                    <i class="fas fa-chart-line me-2"></i>نظام المحاسبة
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">لوحة التحكم</a>
                    <a class="nav-link" href="/logout">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="card-title mb-1">
                                        <i class="fas fa-satellite-dish text-primary me-2"></i>إدارة الوصول عن بُعد
                                    </h4>
                                    <p class="text-muted mb-0">إدارة رموز الوصول للصيانة والدعم التقني</p>
                                </div>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTokenModal">
                                    <i class="fas fa-plus me-2"></i>إنشاء رمز جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-key fa-2x text-primary mb-2"></i>
                            <h5>{{ stats.total_tokens }}</h5>
                            <small class="text-muted">إجمالي الرموز</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5>{{ stats.active_tokens }}</h5>
                            <small class="text-muted">رموز نشطة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-plug fa-2x text-info mb-2"></i>
                            <h5>{{ stats.total_connections }}</h5>
                            <small class="text-muted">إجمالي الاتصالات</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Access Tokens -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">رموز الوصول</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>معرف العميل</th>
                                            <th>الرمز</th>
                                            <th>الحالة</th>
                                            <th>آخر اتصال</th>
                                            <th>عدد الاتصالات</th>
                                            <th>تاريخ الانتهاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if access_tokens %}
                                        {% for token in access_tokens %}
                                        <tr>
                                            <td><strong>{{ token.client_name }}</strong></td>
                                            <td><code>{{ token.client_id }}</code></td>
                                            <td>
                                                <div class="token-display">
                                                    <span id="token-{{ token.id }}">{{ token.access_token[:8] }}...</span>
                                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="toggleToken({{ token.id }}, '{{ token.access_token }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>
                                                {% if token.is_active %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                    <span class="badge bg-danger">معطل</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ token.last_connection or 'لم يتصل بعد' }}</td>
                                            <td>{{ token.connection_count }}</td>
                                            <td>{{ token.expires_at }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    {% if token.is_active %}
                                                    <button class="btn btn-outline-warning" onclick="toggleTokenStatus({{ token.id }}, 0)">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                    {% else %}
                                                    <button class="btn btn-outline-success" onclick="toggleTokenStatus({{ token.id }}, 1)">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                    {% endif %}
                                                    <button class="btn btn-outline-danger" onclick="deleteToken({{ token.id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        {% else %}
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-4">
                                                <i class="fas fa-key fa-2x mb-3"></i>
                                                <br>لا توجد رموز وصول
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Connections -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">آخر الاتصالات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الوقت</th>
                                            <th>معرف العميل</th>
                                            <th>العملية</th>
                                            <th>عنوان IP</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for conn in recent_connections %}
                                        <tr>
                                            <td>{{ conn.timestamp }}</td>
                                            <td><code>{{ conn.client_id }}</code></td>
                                            <td>{{ conn.operation }}</td>
                                            <td>{{ conn.ip_address }}</td>
                                            <td>
                                                {% if conn.status == 'success' %}
                                                    <span class="badge bg-success">نجح</span>
                                                {% else %}
                                                    <span class="badge bg-danger">فشل</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Token Modal -->
        <div class="modal fade" id="createTokenModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إنشاء رمز وصول جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="/remote-access/create" method="POST">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" name="client_name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">معرف العميل</label>
                                <input type="text" class="form-control" name="client_id" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">مدة الصلاحية (أيام)</label>
                                <select class="form-control" name="validity_days">
                                    <option value="7">7 أيام</option>
                                    <option value="30">30 يوم</option>
                                    <option value="90">90 يوم</option>
                                    <option value="365">سنة واحدة</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العمليات المسموحة</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="operations" value="view" checked>
                                    <label class="form-check-label">عرض البيانات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="operations" value="edit">
                                    <label class="form-check-label">تعديل البيانات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="operations" value="backup">
                                    <label class="form-check-label">النسخ الاحتياطي</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="operations" value="system">
                                    <label class="form-check-label">إعدادات النظام</label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">إنشاء الرمز</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function toggleToken(tokenId, fullToken) {
                const element = document.getElementById('token-' + tokenId);
                if (element.textContent.includes('...')) {
                    element.textContent = fullToken;
                } else {
                    element.textContent = fullToken.substring(0, 8) + '...';
                }
            }

            function toggleTokenStatus(tokenId, status) {
                if (confirm('هل تريد تغيير حالة هذا الرمز؟')) {
                    fetch(`/remote-access/${tokenId}/toggle`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ status: status })
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        }
                    });
                }
            }

            function deleteToken(tokenId) {
                if (confirm('هل تريد حذف هذا الرمز نهائياً؟')) {
                    fetch(`/remote-access/${tokenId}/delete`, {
                        method: 'POST'
                    }).then(response => {
                        if (response.ok) {
                            location.reload();
                        }
                    });
                }
            }
        </script>
    </body>
    </html>
    '''

    return render_template_string(remote_template,
                                access_tokens=access_tokens,
                                recent_connections=recent_connections,
                                stats=stats,
                                session=session)

# مسار إنشاء رمز وصول عن بُعد
@app.route('/remote-access/create', methods=['POST'])
def create_remote_token():
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        client_name = request.form.get('client_name')
        client_id = request.form.get('client_id')
        validity_days = int(request.form.get('validity_days', 30))
        operations = request.form.getlist('operations')

        # إنشاء رمز آمن
        access_token = generate_remote_token()

        # حساب تاريخ الانتهاء
        from datetime import datetime, timedelta
        expires_at = datetime.now() + timedelta(days=validity_days)

        conn = get_db_connection()
        conn.execute('''
            INSERT INTO remote_access (access_token, client_name, client_id, allowed_operations, expires_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (access_token, client_name, client_id, ','.join(operations), expires_at))

        conn.commit()
        conn.close()

        # تسجيل في سجل التدقيق
        log_audit('إنشاء رمز وصول عن بُعد', 'remote_access', None, None, {
            'client_name': client_name, 'client_id': client_id, 'validity_days': validity_days
        })

        flash(f'تم إنشاء رمز الوصول للعميل "{client_name}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء الرمز: {str(e)}', 'error')

    return redirect(url_for('remote_access'))

# مسار تغيير حالة رمز الوصول
@app.route('/remote-access/<int:token_id>/toggle', methods=['POST'])
def toggle_remote_token(token_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        data = request.get_json()
        status = data.get('status', 0)

        conn = get_db_connection()
        conn.execute('UPDATE remote_access SET is_active = ? WHERE id = ?', (status, token_id))
        conn.commit()
        conn.close()

        action = 'تفعيل' if status else 'إلغاء تفعيل'
        log_audit(f'{action} رمز وصول عن بُعد', 'remote_access', token_id, None, {'status': status})

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# مسار حذف رمز الوصول
@app.route('/remote-access/<int:token_id>/delete', methods=['POST'])
def delete_remote_token(token_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        conn = get_db_connection()

        # جلب معلومات الرمز قبل الحذف
        token_info = conn.execute('SELECT client_name FROM remote_access WHERE id = ?', (token_id,)).fetchone()

        conn.execute('DELETE FROM remote_access WHERE id = ?', (token_id,))
        conn.commit()
        conn.close()

        log_audit('حذف رمز وصول عن بُعد', 'remote_access', token_id, None, {
            'client_name': token_info['client_name'] if token_info else 'غير معروف'
        })

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API للوصول عن بُعد
@app.route('/api/remote/<operation>', methods=['GET', 'POST'])
def remote_api(operation):
    # التحقق من رمز الوصول
    token = request.headers.get('Authorization', '').replace('Bearer ', '')
    if not token:
        log_remote_connection('unknown', operation, 'failed', 'لا يوجد رمز وصول')
        return jsonify({'error': 'رمز الوصول مطلوب'}), 401

    access_info = verify_remote_access(token)
    if not access_info:
        log_remote_connection('unknown', operation, 'failed', 'رمز وصول غير صحيح')
        return jsonify({'error': 'رمز وصول غير صحيح أو منتهي الصلاحية'}), 401

    client_id = access_info['client_id']
    allowed_operations = access_info['allowed_operations'].split(',') if access_info['allowed_operations'] else []

    # التحقق من الصلاحيات
    if operation not in allowed_operations:
        log_remote_connection(client_id, operation, 'failed', 'عملية غير مسموحة')
        return jsonify({'error': 'عملية غير مسموحة'}), 403

    try:
        conn = get_db_connection()

        if operation == 'view':
            # عرض إحصائيات النظام
            stats = {
                'products_count': conn.execute('SELECT COUNT(*) as count FROM products').fetchone()['count'],
                'customers_count': conn.execute('SELECT COUNT(*) as count FROM customers').fetchone()['count'],
                'invoices_count': conn.execute('SELECT COUNT(*) as count FROM invoices').fetchone()['count'],
                'system_status': 'نشط'
            }
            log_remote_connection(client_id, operation, 'success', 'عرض إحصائيات النظام')
            return jsonify(stats)

        elif operation == 'backup':
            # إنشاء نسخة احتياطية
            import shutil
            import zipfile
            from datetime import datetime

            backup_name = f"remote_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

            with zipfile.ZipFile(backup_name, 'w') as zipf:
                zipf.write('accounting_system.db', 'accounting_system.db')

            log_remote_connection(client_id, operation, 'success', f'تم إنشاء نسخة احتياطية: {backup_name}')
            return jsonify({'success': True, 'backup_file': backup_name})

        elif operation == 'system':
            # معلومات النظام
            import psutil
            import platform

            system_info = {
                'platform': platform.system(),
                'python_version': platform.python_version(),
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
            }

            log_remote_connection(client_id, operation, 'success', 'عرض معلومات النظام')
            return jsonify(system_info)

        conn.close()

    except Exception as e:
        log_remote_connection(client_id, operation, 'failed', str(e))
        return jsonify({'error': str(e)}), 500

# صفحة التقارير
@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # إحصائيات عامة للتقارير
    total_revenue = conn.execute('SELECT SUM(total_amount) as total FROM invoices WHERE status = "paid"').fetchone()['total'] or 0
    total_expenses = conn.execute('SELECT SUM(amount) as total FROM expenses WHERE status = "paid"').fetchone()['total'] or 0
    net_profit = total_revenue - total_expenses

    # بيانات المبيعات الشهرية (آخر 6 أشهر)
    monthly_sales = conn.execute('''
        SELECT strftime('%Y-%m', issue_date) as month, SUM(total_amount) as total
        FROM invoices
        WHERE status = 'paid' AND issue_date >= date('now', '-6 months')
        GROUP BY strftime('%Y-%m', issue_date)
        ORDER BY month
    ''').fetchall()

    # أفضل المنتجات مبيعاً
    top_products = conn.execute('''
        SELECT p.name, SUM(ii.quantity) as total_quantity, SUM(ii.quantity * ii.unit_price) as total_sales
        FROM invoice_items ii
        JOIN products p ON ii.product_id = p.id
        JOIN invoices i ON ii.invoice_id = i.id
        WHERE i.status = 'paid'
        GROUP BY p.id, p.name
        ORDER BY total_sales DESC
        LIMIT 5
    ''').fetchall()

    conn.close()

    reports_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-chart-bar me-2 text-primary"></i>التقارير المالية</h2>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-arrow-up text-success"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(total_revenue) }}</div>
                    <div class="text-muted">إجمالي الإيرادات (ر.س)</div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-arrow-down text-danger"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(total_expenses) }}</div>
                    <div class="text-muted">إجمالي المصروفات (ر.س)</div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-chart-line text-primary"></i>
                    </div>
                    <div class="stats-number">{{ "{:,.0f}".format(net_profit) }}</div>
                    <div class="text-muted">صافي الربح (ر.س)</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- رسم بياني للمبيعات -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">المبيعات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- أفضل المنتجات -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">أفضل المنتجات مبيعاً</h5>
                    </div>
                    <div class="card-body">
                        {% for product in top_products %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-0">{{ product.name }}</h6>
                                <small class="text-muted">{{ product.total_quantity }} وحدة</small>
                            </div>
                            <span class="badge bg-primary">{{ "{:,.0f}".format(product.total_sales) }} ر.س</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التقارير -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">تقارير مفصلة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 py-3" onclick="generateReport('sales')">
                                    <i class="fas fa-chart-line fa-2x mb-2 d-block"></i>
                                    تقرير المبيعات
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 py-3" onclick="generateReport('expenses')">
                                    <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                                    تقرير المصروفات
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 py-3" onclick="generateReport('profit')">
                                    <i class="fas fa-chart-pie fa-2x mb-2 d-block"></i>
                                    تقرير الأرباح
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 py-3" onclick="generateReport('inventory')">
                                    <i class="fas fa-boxes fa-2x mb-2 d-block"></i>
                                    تقرير المخزون
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // رسم بياني للمبيعات
            const ctx = document.getElementById('salesChart').getContext('2d');
            const salesData = {{ monthly_sales_json | safe }};

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: salesData.map(item => item.month),
                    datasets: [{
                        label: 'المبيعات (ر.س)',
                        data: salesData.map(item => item.total),
                        borderColor: '#2E86AB',
                        backgroundColor: 'rgba(46, 134, 171, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            function generateReport(type) {
                alert(`سيتم إنشاء تقرير ${type} قريباً`);
            }
        </script>
    ''')

    import json

    return render_template_string(reports_template,
                                total_revenue=total_revenue,
                                total_expenses=total_expenses,
                                net_profit=net_profit,
                                top_products=top_products,
                                monthly_sales_json=json.dumps([dict(row) for row in monthly_sales]),
                                session=session)

# مسار عرض الفاتورة للطباعة
@app.route('/invoices/<int:invoice_id>/print')
def print_invoice(invoice_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # جلب بيانات الفاتورة
    invoice = conn.execute('''
        SELECT i.*, c.name as customer_name, c.address as customer_address,
               c.phone as customer_phone, c.tax_number as customer_tax_number,
               u.first_name || ' ' || u.last_name as created_by_name
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        LEFT JOIN users u ON i.created_by = u.id
        WHERE i.id = ?
    ''', (invoice_id,)).fetchone()

    if not invoice:
        flash('الفاتورة غير موجودة', 'error')
        return redirect(url_for('invoices'))

    # جلب بنود الفاتورة
    items = conn.execute('''
        SELECT ii.*, p.name as product_name, p.unit
        FROM invoice_items ii
        JOIN products p ON ii.product_id = p.id
        WHERE ii.invoice_id = ?
    ''', (invoice_id,)).fetchall()

    conn.close()

    # قالب طباعة الفاتورة
    print_template = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فاتورة رقم {{ invoice.invoice_number }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            * { font-family: 'Cairo', sans-serif; }
            body { background: white; }
            .invoice-header { border-bottom: 3px solid #2E86AB; padding-bottom: 20px; margin-bottom: 30px; }
            .company-info { text-align: center; }
            .invoice-title { color: #2E86AB; font-size: 2rem; font-weight: bold; }
            .invoice-details { background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .table th { background: #2E86AB; color: white; }
            .total-section { background: #f8f9fa; padding: 20px; border-radius: 8px; }
            @media print {
                .no-print { display: none !important; }
                body { margin: 0; }
                .container { max-width: none; }
            }
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <!-- أزرار الطباعة -->
            <div class="no-print mb-3">
                <button onclick="window.print()" class="btn btn-primary me-2">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button onclick="downloadPDF()" class="btn btn-success me-2">
                    <i class="fas fa-download me-2"></i>تحميل PDF
                </button>
                <a href="/invoices" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
            </div>

            <!-- رأس الفاتورة -->
            <div class="invoice-header">
                <div class="row">
                    <div class="col-md-6">
                        <div class="company-info">
                            <h1 class="invoice-title">نظام المحاسبة المتكامل</h1>
                            <p class="mb-1">شركة التقنية المتقدمة</p>
                            <p class="mb-1">الرياض، المملكة العربية السعودية</p>
                            <p class="mb-1">هاتف: 0112345678 | بريد: <EMAIL></p>
                            <p class="mb-0">الرقم الضريبي: 300123456789003</p>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <h2 class="text-primary">فاتورة</h2>
                        <div class="invoice-details">
                            <p class="mb-1"><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                            <p class="mb-1"><strong>تاريخ الإصدار:</strong> {{ invoice.issue_date }}</p>
                            {% if invoice.due_date %}
                            <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date }}</p>
                            {% endif %}
                            <p class="mb-0"><strong>أنشأها:</strong> {{ invoice.created_by_name }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات العميل -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>فاتورة إلى:</h5>
                    <div class="border p-3 rounded">
                        <h6>{{ invoice.customer_name or 'عميل نقدي' }}</h6>
                        {% if invoice.customer_address %}
                        <p class="mb-1">{{ invoice.customer_address }}</p>
                        {% endif %}
                        {% if invoice.customer_phone %}
                        <p class="mb-1">هاتف: {{ invoice.customer_phone }}</p>
                        {% endif %}
                        {% if invoice.customer_tax_number %}
                        <p class="mb-0">الرقم الضريبي: {{ invoice.customer_tax_number }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>تفاصيل الدفع:</h5>
                    <div class="border p-3 rounded">
                        <p class="mb-1"><strong>طريقة الدفع:</strong>
                            {% if invoice.payment_method == 'cash' %}نقدي
                            {% elif invoice.payment_method == 'card' %}بطاقة
                            {% elif invoice.payment_method == 'transfer' %}تحويل
                            {% elif invoice.payment_method == 'credit' %}آجل
                            {% else %}{{ invoice.payment_method }}
                            {% endif %}
                        </p>
                        <p class="mb-1"><strong>الحالة:</strong>
                            {% if invoice.status == 'draft' %}مسودة
                            {% elif invoice.status == 'sent' %}مرسلة
                            {% elif invoice.status == 'paid' %}مدفوعة
                            {% elif invoice.status == 'overdue' %}متأخرة
                            {% else %}{{ invoice.status }}
                            {% endif %}
                        </p>
                        <p class="mb-0"><strong>المبلغ المدفوع:</strong> {{ "{:,.2f}".format(invoice.paid_amount) }} ر.س</p>
                    </div>
                </div>
            </div>

            <!-- جدول البنود -->
            <div class="table-responsive mb-4">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>الوحدة</th>
                            <th>سعر الوحدة</th>
                            <th>الخصم %</th>
                            <th>الضريبة %</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.product_name }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit }}</td>
                            <td>{{ "{:,.2f}".format(item.unit_price) }} ر.س</td>
                            <td>{{ item.discount_rate }}%</td>
                            <td>{{ item.tax_rate }}%</td>
                            <td>{{ "{:,.2f}".format((item.quantity * item.unit_price) * (1 - item.discount_rate/100) * (1 + item.tax_rate/100)) }} ر.س</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- إجماليات الفاتورة -->
            <div class="row">
                <div class="col-md-6">
                    {% if invoice.notes %}
                    <h6>ملاحظات:</h6>
                    <p class="border p-3 rounded">{{ invoice.notes }}</p>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <div class="total-section">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>{{ "{:,.2f}".format(invoice.subtotal) }} ر.س</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الخصم:</span>
                            <span>{{ "{:,.2f}".format(invoice.discount_amount) }} ر.س</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة (15%):</span>
                            <span>{{ "{:,.2f}".format(invoice.tax_amount) }} ر.س</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>الإجمالي النهائي:</strong>
                            <strong>{{ "{:,.2f}".format(invoice.total_amount) }} ر.س</strong>
                        </div>
                        {% if invoice.paid_amount > 0 %}
                        <div class="d-flex justify-content-between mt-2">
                            <span>المبلغ المدفوع:</span>
                            <span>{{ "{:,.2f}".format(invoice.paid_amount) }} ر.س</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <strong>المبلغ المستحق:</strong>
                            <strong>{{ "{:,.2f}".format(invoice.total_amount - invoice.paid_amount) }} ر.س</strong>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- تذييل الفاتورة -->
            <div class="text-center mt-5 pt-4 border-top">
                <p class="text-muted">شكراً لتعاملكم معنا</p>
                <p class="small text-muted">تم إنشاء هذه الفاتورة بواسطة نظام المحاسبة المتكامل</p>
            </div>
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
        <script>
            function downloadPDF() {
                // إخفاء أزرار الطباعة مؤقتاً
                document.querySelector('.no-print').style.display = 'none';

                // استخدام window.print مع CSS للطباعة
                window.print();

                // إظهار الأزرار مرة أخرى
                setTimeout(() => {
                    document.querySelector('.no-print').style.display = 'block';
                }, 1000);
            }
        </script>
    </body>
    </html>
    '''

    return render_template_string(print_template,
                                invoice=invoice,
                                items=items)

# دالة للحصول على الإشعارات
def get_notifications():
    """جلب جميع الإشعارات المهمة"""
    conn = get_db_connection()
    notifications = []

    # تنبيهات المخزون المنخفض
    low_stock_products = conn.execute('''
        SELECT name, current_stock, min_stock_level
        FROM products
        WHERE current_stock <= min_stock_level AND is_active = 1
    ''').fetchall()

    for product in low_stock_products:
        notifications.append({
            'type': 'warning',
            'icon': 'fas fa-exclamation-triangle',
            'title': 'مخزون منخفض',
            'message': f'المنتج "{product["name"]}" وصل للحد الأدنى ({product["current_stock"]} متبقي)',
            'time': 'الآن',
            'action_url': '/products',
            'priority': 'high'
        })

    # تنبيهات الفواتير المتأخرة
    from datetime import date, timedelta
    overdue_invoices = conn.execute('''
        SELECT invoice_number, customer_id, due_date, total_amount
        FROM invoices
        WHERE due_date < ? AND status != 'paid' AND due_date IS NOT NULL
    ''', (date.today(),)).fetchall()

    for invoice in overdue_invoices:
        notifications.append({
            'type': 'danger',
            'icon': 'fas fa-clock',
            'title': 'فاتورة متأخرة',
            'message': f'الفاتورة {invoice["invoice_number"]} متأخرة عن موعد الاستحقاق',
            'time': 'متأخرة',
            'action_url': '/invoices',
            'priority': 'high'
        })

    # تنبيهات المصروفات في انتظار الموافقة
    pending_expenses = conn.execute('''
        SELECT COUNT(*) as count FROM expenses WHERE status = 'pending'
    ''').fetchone()

    if pending_expenses['count'] > 0:
        notifications.append({
            'type': 'info',
            'icon': 'fas fa-clock',
            'title': 'مصروفات في انتظار الموافقة',
            'message': f'{pending_expenses["count"]} مصروف في انتظار الموافقة',
            'time': 'جديد',
            'action_url': '/expenses',
            'priority': 'medium'
        })

    # تنبيهات الموظفين الجدد (آخر 7 أيام)
    new_employees = conn.execute('''
        SELECT COUNT(*) as count FROM employees
        WHERE created_at >= date('now', '-7 days')
    ''').fetchone()

    if new_employees['count'] > 0:
        notifications.append({
            'type': 'success',
            'icon': 'fas fa-user-plus',
            'title': 'موظفين جدد',
            'message': f'تم إضافة {new_employees["count"]} موظف جديد هذا الأسبوع',
            'time': 'هذا الأسبوع',
            'action_url': '/payroll',
            'priority': 'low'
        })

    conn.close()

    # ترتيب الإشعارات حسب الأولوية
    priority_order = {'high': 0, 'medium': 1, 'low': 2}
    notifications.sort(key=lambda x: priority_order.get(x['priority'], 3))

    return notifications

# مسار API للحصول على الإشعارات
@app.route('/api/notifications')
def api_notifications():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    notifications = get_notifications()
    return jsonify({
        'notifications': notifications,
        'count': len(notifications),
        'unread_count': len([n for n in notifications if n['priority'] == 'high'])
    })

# صفحة الإشعارات
@app.route('/notifications')
def notifications_page():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    notifications = get_notifications()

    notifications_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-bell me-2 text-primary"></i>الإشعارات والتنبيهات</h2>
            </div>
        </div>

        <!-- إحصائيات الإشعارات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="stats-number">{{ notifications|length }}</div>
                    <div class="text-muted">إجمالي الإشعارات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                    </div>
                    <div class="stats-number">{{ notifications|selectattr('priority', 'equalto', 'high')|list|length }}</div>
                    <div class="text-muted">عالية الأولوية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-info-circle text-warning"></i>
                    </div>
                    <div class="stats-number">{{ notifications|selectattr('priority', 'equalto', 'medium')|list|length }}</div>
                    <div class="text-muted">متوسطة الأولوية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div class="stats-number">{{ notifications|selectattr('priority', 'equalto', 'low')|list|length }}</div>
                    <div class="text-muted">منخفضة الأولوية</div>
                </div>
            </div>
        </div>

        <!-- قائمة الإشعارات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">الإشعارات الحديثة</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshNotifications()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        {% if notifications %}
                        <div class="list-group list-group-flush">
                            {% for notification in notifications %}
                            <div class="list-group-item border-0 py-3 notification-item" data-priority="{{ notification.priority }}">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="notification-icon bg-{{ notification.type }} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="{{ notification.icon }}"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ notification.title }}</h6>
                                                <p class="mb-1 text-muted">{{ notification.message }}</p>
                                                <small class="text-muted">{{ notification.time }}</small>
                                            </div>
                                            <div class="ms-3">
                                                {% if notification.priority == 'high' %}
                                                    <span class="badge bg-danger">عاجل</span>
                                                {% elif notification.priority == 'medium' %}
                                                    <span class="badge bg-warning">متوسط</span>
                                                {% else %}
                                                    <span class="badge bg-info">عادي</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% if notification.action_url %}
                                        <div class="mt-2">
                                            <a href="{{ notification.action_url }}" class="btn btn-sm btn-outline-primary">
                                                عرض التفاصيل
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات جديدة</h5>
                            <p class="text-muted">جميع الأمور تسير بشكل طبيعي</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <script>
            function refreshNotifications() {
                location.reload();
            }

            // تحديث الإشعارات كل 30 ثانية
            setInterval(function() {
                fetch('/api/notifications')
                    .then(response => response.json())
                    .then(data => {
                        // تحديث عداد الإشعارات في شريط القوائم
                        updateNotificationBadge(data.unread_count);
                    });
            }, 30000);

            function updateNotificationBadge(count) {
                const badge = document.getElementById('notificationBadge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }
        </script>

        <style>
            .notification-item[data-priority="high"] {
                border-right: 4px solid #dc3545;
                background: rgba(220, 53, 69, 0.05);
            }
            .notification-item[data-priority="medium"] {
                border-right: 4px solid #ffc107;
                background: rgba(255, 193, 7, 0.05);
            }
            .notification-item[data-priority="low"] {
                border-right: 4px solid #17a2b8;
                background: rgba(23, 162, 184, 0.05);
            }
            .notification-icon {
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        </style>
    ''')

    return render_template_string(notifications_template,
                                notifications=notifications,
                                session=session)

# نظام النسخ الاحتياطي
import shutil
import zipfile
from datetime import datetime
import threading
import time

def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # اسم ملف النسخة الاحتياطية مع التاريخ والوقت
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'accounting_backup_{timestamp}.zip'
        backup_path = os.path.join(backup_dir, backup_filename)

        # إنشاء ملف ZIP يحتوي على قاعدة البيانات
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة قاعدة البيانات
            if os.path.exists('accounting_system.db'):
                zipf.write('accounting_system.db', 'accounting_system.db')

            # إضافة ملف معلومات النسخة الاحتياطية
            backup_info = f"""
نسخة احتياطية لنظام المحاسبة المتكامل
تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
حجم قاعدة البيانات: {os.path.getsize('accounting_system.db') if os.path.exists('accounting_system.db') else 0} بايت
إصدار النظام: 2.0
"""
            zipf.writestr('backup_info.txt', backup_info)

        # حذف النسخ القديمة (الاحتفاظ بآخر 10 نسخ فقط)
        cleanup_old_backups(backup_dir)

        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path

    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def cleanup_old_backups(backup_dir, keep_count=10):
    """حذف النسخ الاحتياطية القديمة"""
    try:
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.startswith('accounting_backup_') and filename.endswith('.zip'):
                filepath = os.path.join(backup_dir, filename)
                backup_files.append((filepath, os.path.getctime(filepath)))

        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backup_files.sort(key=lambda x: x[1], reverse=True)

        # حذف النسخ الزائدة
        for filepath, _ in backup_files[keep_count:]:
            os.remove(filepath)
            print(f"🗑️ تم حذف النسخة القديمة: {os.path.basename(filepath)}")

    except Exception as e:
        print(f"❌ خطأ في تنظيف النسخ القديمة: {e}")

def auto_backup_worker():
    """عامل النسخ الاحتياطي التلقائي"""
    while True:
        try:
            # انتظار 24 ساعة (86400 ثانية)
            time.sleep(86400)

            # إنشاء نسخة احتياطية
            create_backup()

        except Exception as e:
            print(f"❌ خطأ في النسخ الاحتياطي التلقائي: {e}")
            time.sleep(3600)  # انتظار ساعة واحدة قبل المحاولة مرة أخرى

# مسار إنشاء نسخة احتياطية يدوية
@app.route('/backup/create')
def manual_backup():
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        flash('غير مصرح لك بإنشاء نسخ احتياطية', 'error')
        return redirect(url_for('dashboard'))

    backup_path = create_backup()
    if backup_path:
        flash('تم إنشاء النسخة الاحتياطية بنجاح!', 'success')
    else:
        flash('فشل في إنشاء النسخة الاحتياطية', 'error')

    return redirect(url_for('settings'))

# مسار تحميل النسخة الاحتياطية
@app.route('/backup/download/<filename>')
def download_backup(filename):
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        flash('غير مصرح لك بتحميل النسخ الاحتياطية', 'error')
        return redirect(url_for('dashboard'))

    backup_path = os.path.join('backups', filename)
    if os.path.exists(backup_path):
        return send_file(backup_path, as_attachment=True)
    else:
        flash('الملف غير موجود', 'error')
        return redirect(url_for('settings'))

# صفحة الإعدادات
@app.route('/settings')
def settings():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # جلب قائمة النسخ الاحتياطية
    backup_files = []
    backup_dir = 'backups'
    if os.path.exists(backup_dir):
        for filename in os.listdir(backup_dir):
            if filename.startswith('accounting_backup_') and filename.endswith('.zip'):
                filepath = os.path.join(backup_dir, filename)
                file_size = os.path.getsize(filepath)
                file_date = datetime.fromtimestamp(os.path.getctime(filepath))
                backup_files.append({
                    'filename': filename,
                    'size': file_size,
                    'date': file_date,
                    'size_mb': round(file_size / (1024 * 1024), 2)
                })

    # ترتيب حسب التاريخ (الأحدث أولاً)
    backup_files.sort(key=lambda x: x['date'], reverse=True)

    # إحصائيات النظام
    conn = get_db_connection()
    system_stats = {
        'db_size': os.path.getsize('accounting_system.db') if os.path.exists('accounting_system.db') else 0,
        'total_users': conn.execute('SELECT COUNT(*) as count FROM users').fetchone()['count'],
        'total_customers': conn.execute('SELECT COUNT(*) as count FROM customers').fetchone()['count'],
        'total_products': conn.execute('SELECT COUNT(*) as count FROM products').fetchone()['count'],
        'total_invoices': conn.execute('SELECT COUNT(*) as count FROM invoices').fetchone()['count'],
        'backup_count': len(backup_files)
    }
    conn.close()

    settings_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-cog me-2 text-primary"></i>إعدادات النظام</h2>
            </div>
        </div>

        <!-- إحصائيات النظام -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stats-number">{{ "{:.1f}".format(system_stats.db_size / (1024*1024)) }}</div>
                    <div class="text-muted">حجم قاعدة البيانات (MB)</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users text-info"></i>
                    </div>
                    <div class="stats-number">{{ system_stats.total_users }}</div>
                    <div class="text-muted">إجمالي المستخدمين</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice text-success"></i>
                    </div>
                    <div class="stats-number">{{ system_stats.total_invoices }}</div>
                    <div class="text-muted">إجمالي الفواتير</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-save text-warning"></i>
                    </div>
                    <div class="stats-number">{{ system_stats.backup_count }}</div>
                    <div class="text-muted">النسخ الاحتياطية</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- النسخ الاحتياطية -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">النسخ الاحتياطية</h5>
                            {% if session.user_role == 'admin' %}
                            <a href="/backup/create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        {% if backup_files %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>التاريخ</th>
                                        <th>الحجم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backup_files %}
                                    <tr>
                                        <td>{{ backup.filename }}</td>
                                        <td>{{ backup.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>{{ backup.size_mb }} MB</td>
                                        <td>
                                            {% if session.user_role == 'admin' %}
                                            <a href="/backup/download/{{ backup.filename }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download me-1"></i>تحميل
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-save fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد نسخ احتياطية</h6>
                            {% if session.user_role == 'admin' %}
                            <p class="text-muted">قم بإنشاء أول نسخة احتياطية</p>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>إصدار النظام:</strong>
                            <span class="badge bg-primary">2.0</span>
                        </div>
                        <div class="mb-3">
                            <strong>قاعدة البيانات:</strong>
                            <span class="text-success">SQLite</span>
                        </div>
                        <div class="mb-3">
                            <strong>النسخ الاحتياطي التلقائي:</strong>
                            <span class="badge bg-success">مفعل</span>
                        </div>
                        <div class="mb-3">
                            <strong>آخر نسخة احتياطية:</strong>
                            {% if backup_files %}
                            <small class="text-muted">{{ backup_files[0].date.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% else %}
                            <small class="text-muted">لم يتم إنشاء نسخ احتياطية</small>
                            {% endif %}
                        </div>

                        <hr>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> يتم إنشاء نسخة احتياطية تلقائياً كل 24 ساعة
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ''')

    return render_template_string(settings_template,
                                backup_files=backup_files,
                                system_stats=system_stats,
                                session=session)

# نظام الفاتورة الإلكترونية
@app.route('/e-invoice')
def e_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()

    # إضافة أعمدة الفاتورة الإلكترونية إذا لم تكن موجودة
    try:
        conn.execute('ALTER TABLE invoices ADD COLUMN e_invoice_status TEXT')
        conn.execute('ALTER TABLE invoices ADD COLUMN e_invoice_uuid TEXT')
        conn.execute('ALTER TABLE invoices ADD COLUMN e_invoice_submitted_at TIMESTAMP')
        conn.commit()
    except:
        pass  # الأعمدة موجودة بالفعل

    # جلب جميع الفواتير (مع إمكانية تحويلها لإلكترونية)
    e_invoices = conn.execute('''
        SELECT i.*, c.name as customer_name, c.tax_number as customer_tax_number
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        ORDER BY i.created_at DESC
        LIMIT 50
    ''').fetchall()

    # إحصائيات الفواتير الإلكترونية
    stats = {
        'total_e_invoices': len(e_invoices),
        'submitted': len([i for i in e_invoices if i['e_invoice_status'] == 'submitted']),
        'approved': len([i for i in e_invoices if i['e_invoice_status'] == 'approved']),
        'rejected': len([i for i in e_invoices if i['e_invoice_status'] == 'rejected']),
        'pending': len([i for i in e_invoices if i['e_invoice_status'] is None])
    }

    conn.close()

    e_invoice_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-file-invoice-dollar me-2 text-primary"></i>الفاتورة الإلكترونية</h2>
                    <div>
                        <button class="btn btn-success me-2" onclick="generateEInvoice()">
                            <i class="fas fa-plus me-2"></i>إنشاء فاتورة إلكترونية
                        </button>
                        <button class="btn btn-info" onclick="syncWithZatca()">
                            <i class="fas fa-sync me-2"></i>مزامنة مع زاتكا
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الفواتير الإلكترونية -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_e_invoices }}</div>
                    <div class="text-muted">إجمالي الفواتير الإلكترونية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-paper-plane text-info"></i>
                    </div>
                    <div class="stats-number">{{ stats.submitted }}</div>
                    <div class="text-muted">مرسلة لزاتكا</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div class="stats-number">{{ stats.approved }}</div>
                    <div class="text-muted">معتمدة</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-times-circle text-danger"></i>
                    </div>
                    <div class="stats-number">{{ stats.rejected }}</div>
                    <div class="text-muted">مرفوضة</div>
                </div>
            </div>
        </div>

        <!-- معلومات زاتكا -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5><i class="fas fa-info-circle me-2"></i>نظام الفاتورة الإلكترونية - زاتكا</h5>
                            <p class="mb-0">متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك السعودية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-success fs-6">متوافق مع زاتكا</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الفواتير الإلكترونية -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة الفواتير الإلكترونية</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>حالة زاتكا</th>
                                        <th>رقم التتبع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if e_invoices %}
                                    {% for invoice in e_invoices %}
                                    <tr>
                                        <td><strong>{{ invoice.invoice_number }}</strong></td>
                                        <td>{{ invoice.customer_name or 'عميل نقدي' }}</td>
                                        <td>{{ "{:,.2f}".format(invoice.total_amount or 0) }} ر.س</td>
                                        <td>{{ invoice.e_invoice_submitted_at or '-' }}</td>
                                        <td>
                                            {% if invoice.e_invoice_status == 'submitted' %}
                                                <span class="badge bg-info">مرسلة</span>
                                            {% elif invoice.e_invoice_status == 'approved' %}
                                                <span class="badge bg-success">معتمدة</span>
                                            {% elif invoice.e_invoice_status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوضة</span>
                                            {% elif invoice.e_invoice_status is none %}
                                                <span class="badge bg-warning">عادية</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ invoice.e_invoice_status or 'غير محدد' }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ invoice.e_invoice_uuid or '-' }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض الفاتورة" onclick="viewEInvoice('{{ invoice.id }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if invoice.e_invoice_status %}
                                                <button class="btn btn-outline-info" title="تحميل XML" onclick="downloadXML('{{ invoice.id }}')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                {% else %}
                                                <button class="btn btn-outline-success" title="تحويل لإلكترونية" onclick="convertToEInvoice('{{ invoice.id }}')">
                                                    <i class="fas fa-bolt"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="fas fa-file-invoice fa-3x text-muted mb-3 d-block"></i>
                                            <h6 class="text-muted">لا توجد فواتير إلكترونية</h6>
                                            <p class="text-muted">ابدأ بإنشاء أول فاتورة إلكترونية</p>
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function generateEInvoice() {
                // إظهار قائمة الفواتير المتاحة للتحويل
                const invoiceId = prompt('أدخل رقم الفاتورة المراد تحويلها لإلكترونية:');
                if (invoiceId) {
                    fetch(`/e-invoice/convert/${invoiceId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => {
                        if (response.ok) {
                            alert('تم تحويل الفاتورة لإلكترونية بنجاح!');
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء تحويل الفاتورة');
                        }
                    });
                }
            }

            function syncWithZatca() {
                if (confirm('هل تريد مزامنة جميع الفواتير مع نظام زاتكا؟')) {
                    // محاكاة المزامنة
                    const btn = event.target;
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري المزامنة...';
                    btn.disabled = true;

                    setTimeout(() => {
                        alert('تم إرسال الفواتير لنظام زاتكا بنجاح!');
                        btn.innerHTML = '<i class="fas fa-sync me-2"></i>مزامنة مع زاتكا';
                        btn.disabled = false;
                        location.reload();
                    }, 3000);
                }
            }

            function viewEInvoice(invoiceId) {
                window.open(`/invoices/${invoiceId}/print`, '_blank');
            }

            function downloadXML(invoiceId) {
                alert('سيتم تحميل ملف XML للفاتورة رقم: ' + invoiceId);
                // هنا يمكن إضافة رابط تحميل فعلي
            }

            function convertToEInvoice(invoiceId) {
                if (confirm('هل تريد تحويل هذه الفاتورة لإلكترونية؟')) {
                    fetch(`/e-invoice/convert/${invoiceId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }).then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم تحويل الفاتورة لإلكترونية بنجاح!\\nرقم التتبع: ' + data.uuid);
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    }).catch(error => {
                        alert('حدث خطأ أثناء تحويل الفاتورة');
                    });
                }
            }
        </script>
    ''')

    return render_template_string(e_invoice_template,
                                e_invoices=e_invoices,
                                stats=stats,
                                session=session)

# مسار تحويل فاتورة لإلكترونية
@app.route('/e-invoice/convert/<int:invoice_id>', methods=['POST'])
def convert_to_e_invoice(invoice_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()

        # التحقق من وجود الفاتورة
        invoice = conn.execute('SELECT * FROM invoices WHERE id = ?', (invoice_id,)).fetchone()
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404

        # تحديث حالة الفاتورة لإلكترونية
        import uuid
        e_invoice_uuid = str(uuid.uuid4())

        conn.execute('''
            UPDATE invoices
            SET e_invoice_status = 'submitted',
                e_invoice_uuid = ?,
                e_invoice_submitted_at = ?
            WHERE id = ?
        ''', (e_invoice_uuid, datetime.now(), invoice_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'uuid': e_invoice_uuid})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# نظام متعدد الفروع
@app.route('/branches')
def branches():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = None
    try:
        conn = get_db_connection()

        # إنشاء جدول الفروع إذا لم يكن موجود
        conn.execute('''
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE,
                address TEXT,
                phone TEXT,
                email TEXT,
                manager_name TEXT,
                is_main_branch BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إنشاء جدول سجل التدقيق
        conn.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                user_name TEXT,
                action TEXT NOT NULL,
                table_name TEXT NOT NULL,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # إنشاء جدول الوصول عن بُعد
        conn.execute('''
            CREATE TABLE IF NOT EXISTS remote_access (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                access_token TEXT UNIQUE,
                client_name TEXT NOT NULL,
                client_id TEXT UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                last_connection TIMESTAMP,
                connection_count INTEGER DEFAULT 0,
                allowed_operations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP
            )
        ''')

        # إنشاء جدول سجل الاتصالات عن بُعد
        conn.execute('''
            CREATE TABLE IF NOT EXISTS remote_connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_id TEXT,
                operation TEXT,
                ip_address TEXT,
                user_agent TEXT,
                status TEXT,
                details TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()

        # جلب الفروع
        branches = conn.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name').fetchall()

        # إضافة الفرع الرئيسي إذا لم يكن موجود
        if not branches:
            conn.execute('''
                INSERT INTO branches (name, code, address, phone, manager_name, is_main_branch, is_active)
                VALUES ('الفرع الرئيسي', 'MAIN', 'الرياض، المملكة العربية السعودية', '0112345678', 'مدير عام', 1, 1)
            ''')
            conn.commit()
            branches = conn.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name').fetchall()

        # إحصائيات الفروع
        stats = {
            'total_branches': len(branches),
            'main_branches': len([b for b in branches if b.get('is_main_branch')]),
            'sub_branches': len([b for b in branches if not b.get('is_main_branch')]),
            'active_branches': len(branches)
        }

        conn.close()

    except Exception as e:
        print(f"خطأ في إدارة الفروع: {e}")
        if conn:
            conn.close()
        # إنشاء فرع افتراضي في حالة الخطأ
        branches = [{'id': 1, 'name': 'الفرع الرئيسي', 'code': 'MAIN', 'address': 'الرياض', 'phone': '0112345678', 'manager_name': 'مدير عام', 'is_main_branch': 1}]
        stats = {
            'total_branches': 1,
            'main_branches': 1,
            'sub_branches': 0,
            'active_branches': 1
        }

    branches_template = BASE_TEMPLATE.replace('{% block content %}{% endblock %}', '''
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-building me-2 text-primary"></i>إدارة الفروع</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                        <i class="fas fa-plus me-2"></i>إضافة فرع جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات الفروع -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_branches }}</div>
                    <div class="text-muted">إجمالي الفروع</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-star text-warning"></i>
                    </div>
                    <div class="stats-number">{{ stats.main_branches }}</div>
                    <div class="text-muted">فروع رئيسية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-code-branch text-info"></i>
                    </div>
                    <div class="stats-number">{{ stats.sub_branches }}</div>
                    <div class="text-muted">فروع فرعية</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div class="stats-number">{{ stats.active_branches }}</div>
                    <div class="text-muted">فروع نشطة</div>
                </div>
            </div>
        </div>

        <!-- جدول الفروع -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">قائمة الفروع</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>اسم الفرع</th>
                                        <th>الكود</th>
                                        <th>العنوان</th>
                                        <th>الهاتف</th>
                                        <th>المدير</th>
                                        <th>النوع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for branch in branches %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="branch-icon me-3">
                                                    {% if branch.is_main_branch %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="fas fa-building text-primary"></i>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ branch.name }}</h6>
                                                    {% if branch.is_main_branch %}
                                                        <small class="text-warning">فرع رئيسي</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td><code>{{ branch.code }}</code></td>
                                        <td>{{ branch.address or '-' }}</td>
                                        <td>{{ branch.phone or '-' }}</td>
                                        <td>{{ branch.manager_name or '-' }}</td>
                                        <td>
                                            {% if branch.is_main_branch %}
                                                <span class="badge bg-warning">رئيسي</span>
                                            {% else %}
                                                <span class="badge bg-info">فرعي</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="عرض" onclick="viewBranch({{ branch.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" title="تعديل" onclick="editBranch({{ branch.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="التقارير" onclick="viewBranchReports({{ branch.id }})">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة فرع جديد -->
        <div class="modal fade" id="addBranchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة فرع جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addBranchForm" method="POST" action="/branches/add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم الفرع *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">كود الفرع *</label>
                                    <input type="text" class="form-control" name="code" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المدير</label>
                                    <input type="text" class="form-control" name="manager_name">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع الفرع</label>
                                    <select class="form-control" name="is_main_branch">
                                        <option value="0">فرع فرعي</option>
                                        <option value="1">فرع رئيسي</option>
                                    </select>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" form="addBranchForm">حفظ الفرع</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function viewBranch(branchId) {
                fetch(`/branches/${branchId}/view`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            alert('خطأ: ' + data.error);
                        } else {
                            alert(`الفرع: ${data.name}\\nالكود: ${data.code}\\nالعنوان: ${data.address || 'غير محدد'}\\nالهاتف: ${data.phone || 'غير محدد'}\\nالمدير: ${data.manager_name || 'غير محدد'}`);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء عرض الفرع');
                    });
            }

            function editBranch(branchId) {
                fetch(`/branches/${branchId}/edit`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            alert('خطأ: ' + data.error);
                        } else {
                            // ملء النموذج بالبيانات الحالية
                            document.querySelector('#addBranchForm input[name="name"]').value = data.name || '';
                            document.querySelector('#addBranchForm input[name="code"]').value = data.code || '';
                            document.querySelector('#addBranchForm input[name="phone"]').value = data.phone || '';
                            document.querySelector('#addBranchForm input[name="email"]').value = data.email || '';
                            document.querySelector('#addBranchForm input[name="manager_name"]').value = data.manager_name || '';
                            document.querySelector('#addBranchForm textarea[name="address"]').value = data.address || '';
                            document.querySelector('#addBranchForm select[name="is_main_branch"]').value = data.is_main_branch || 0;

                            // تغيير عنوان النافذة والنموذج
                            document.querySelector('#addBranchModal .modal-title').textContent = 'تعديل الفرع';
                            document.querySelector('#addBranchForm').action = `/branches/${branchId}/edit`;

                            // إظهار النافذة
                            new bootstrap.Modal(document.getElementById('addBranchModal')).show();
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ أثناء تحميل بيانات الفرع');
                    });
            }

            function viewBranchReports(branchId) {
                alert(`تقارير الفرع رقم: ${branchId}\\n\\nسيتم إضافة التقارير التفصيلية قريباً:\\n- مبيعات الفرع\\n- مصروفات الفرع\\n- أداء الموظفين`);
            }

            // تحسين نموذج إضافة الفرع
            document.getElementById('addBranchForm').addEventListener('submit', function(e) {
                const submitBtn = document.querySelector('button[form="addBranchForm"]');
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
                submitBtn.disabled = true;
            });

            // إعادة تعيين النموذج عند إغلاق النافذة
            document.getElementById('addBranchModal').addEventListener('hidden.bs.modal', function() {
                document.querySelector('#addBranchForm').reset();
                document.querySelector('#addBranchModal .modal-title').textContent = 'إضافة فرع جديد';
                document.querySelector('#addBranchForm').action = '/branches/add';
            });
        </script>
    ''')

    return render_template_string(branches_template,
                                branches=branches,
                                stats=stats,
                                session=session)

# مسار إضافة فرع جديد
@app.route('/branches/add', methods=['POST'])
def add_branch():
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        flash('غير مصرح لك بإضافة فروع', 'error')
        return redirect(url_for('branches'))

    try:
        name = request.form.get('name')
        code = request.form.get('code')
        address = request.form.get('address')
        phone = request.form.get('phone')
        email = request.form.get('email')
        manager_name = request.form.get('manager_name')
        is_main_branch = bool(int(request.form.get('is_main_branch', 0)))

        conn = get_db_connection()

        # التحقق من عدم تكرار الكود
        existing = conn.execute('SELECT id FROM branches WHERE code = ?', (code,)).fetchone()
        if existing:
            flash('كود الفرع موجود مسبقاً', 'error')
            return redirect(url_for('branches'))

        # إدراج الفرع الجديد
        conn.execute('''
            INSERT INTO branches (name, code, address, phone, email, manager_name, is_main_branch)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (name, code, address, phone, email, manager_name, is_main_branch))

        conn.commit()
        conn.close()

        flash(f'تم إضافة الفرع "{name}" بنجاح!', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء إضافة الفرع: {str(e)}', 'error')

    return redirect(url_for('branches'))

# مسار عرض فرع
@app.route('/branches/<int:branch_id>/view')
def view_branch(branch_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        branch = conn.execute('SELECT * FROM branches WHERE id = ?', (branch_id,)).fetchone()
        conn.close()

        if not branch:
            return jsonify({'error': 'الفرع غير موجود'}), 404

        return jsonify(dict(branch))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# مسار تعديل فرع
@app.route('/branches/<int:branch_id>/edit', methods=['GET', 'POST'])
def edit_branch(branch_id):
    if 'user_id' not in session or session.get('user_role') not in ['admin']:
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        conn = get_db_connection()

        if request.method == 'POST':
            name = request.form.get('name')
            code = request.form.get('code')
            address = request.form.get('address')
            phone = request.form.get('phone')
            manager_name = request.form.get('manager_name')

            conn.execute('''
                UPDATE branches
                SET name = ?, code = ?, address = ?, phone = ?, manager_name = ?
                WHERE id = ?
            ''', (name, code, address, phone, manager_name, branch_id))

            conn.commit()
            conn.close()

            flash(f'تم تحديث الفرع "{name}" بنجاح!', 'success')
            return redirect(url_for('branches'))

        # GET request
        branch = conn.execute('SELECT * FROM branches WHERE id = ?', (branch_id,)).fetchone()
        conn.close()

        if not branch:
            return jsonify({'error': 'الفرع غير موجود'}), 404

        return jsonify(dict(branch))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# تحديث لوحة التحكم لتشمل الميزات الجديدة
@app.route('/dashboard/update')
def dashboard_update():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # تحديث الإحصائيات مع الميزات الجديدة
    conn = get_db_connection()

    # جلب الإشعارات
    notifications = get_notifications()
    notification_count = len([n for n in notifications if n['priority'] == 'high'])

    # إحصائيات الفروع
    try:
        branch_count = conn.execute('SELECT COUNT(*) as count FROM branches WHERE is_active = 1').fetchone()['count']
    except:
        branch_count = 1  # الفرع الرئيسي

    conn.close()

    return jsonify({
        'notification_count': notification_count,
        'branch_count': branch_count,
        'status': 'success'
    })

if __name__ == '__main__':
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('accounting_system.db'):
        print("❌ قاعدة البيانات غير موجودة!")
        print("💡 قم بتشغيل: python simple_database.py")
        exit(1)

    print("🚀 تم تشغيل نظام المحاسبة المتكامل مع قاعدة البيانات!")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   مدير المتجر: manager / mgr123")
    print("=" * 60)

    app.run(debug=False, host='0.0.0.0', port=5000)
